// @ts-check
const CONVERT_SINGS_TO_PLACEHOLDERS = false;
const CHUNK_SIZE = 30;

/**
 * Anime Subtitle Translation Pipeline
 *
 * This script performs the following tasks:
 *
 * 1. Processes cleaned English subtitle files (.txt) for translation into Polish.
 * 2. Utilizes the Anthropic API (Claude 3.5 Sonnet model) for high-quality translations.
 * 3. Fetches anime metadata (character details, genres) from AniList API to enhance translation context.
 * 4. Implements a chunking system to process large subtitle files in manageable segments.
 * 5. Provides detailed translation guidelines to the AI model, including Polish-specific instructions.
 * 6. Handles slang, idiomatic expressions, and colloquial language appropriately in translations.
 * 7. Verifies translated output to ensure line count matches the original subtitle file.
 * 8. Implements retry logic for API calls and translation attempts to handle potential errors.
 * 9. Manages file operations for reading input files and writing translated outputs.
 * 10. Implements robust error handling and logging mechanisms:
 *     - Logs information, errors, uncaught exceptions, and unhandled rejections to separate files.
 *     - Sends critical error notifications to a Discord webhook for monitoring.
 * 11. Utilizes color-coded console output for better readability and error distinction.
 * 12. Sanitizes AI model output to correct common translation issues (e.g., Japanese honorifics).
 * 13. Supports potential multi-language translation (currently commented out for French).
 * 14. Preserves special formatting and timing information from original subtitle files.
 * 15. Initiates a subsequent replacing process after translations are complete.
 * 16. Utilizes environment variables for API keys and configuration.
 * 17. Implements graceful shutdown procedures to ensure proper resource cleanup.
 *
 * The script is designed as a crucial component in an automated anime subtitle
 * translation pipeline, focusing on producing high-quality Polish translations
 * while preserving the nuances and context of the original content. It ensures
 * efficient operation, error recovery, and detailed logging for troubleshooting.
 */

import { config } from 'dotenv';
import { promisify } from "util";
config();
import axios from 'axios';
import * as cp from 'child_process';
import fs from 'fs';
import path from 'path';
import Anthropic from '@anthropic-ai/sdk';
import { text } from 'stream/consumers';
// Claude 4 verification system imports
import { verifyTranslation, getVerificationStats } from './tools/claude4-verifier.js';
import { trackImprovement, generateFeedbackForClaude35, createLearningPromptEnhancement } from './tools/improvement-tracker.js';
import { CLAUDE4_CONFIG, MODEL_CONFIG, AVAILABLE_MODELS } from './tools/config.js';
const execAsync = promisify(cp.exec);

/**
 * @constant {Object} COLORS - ANSI color codes for console output formatting.
 */
const COLORS = {
  RESET: '\x1b[0m',
  BLACK: '\x1b[30m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  GRAY: '\x1b[90m',
  DIM: '\x1b[2m',
  BG_BLACK: '\x1b[40m',
  BG_RED: '\x1b[41m',
  BG_GREEN: '\x1b[42m',
  BG_YELLOW: '\x1b[43m',
  BG_BLUE: '\x1b[44m',
  BG_MAGENTA: '\x1b[45m',
  BG_CYAN: '\x1b[46m',
  BG_WHITE: '\x1b[47m',
};

const IS_RUNNING_FILE = 'app/isRunning.txt';
const DIRECTORY_PATH = 'app/2translate/toTranslate';
const OUTPUT_DIRECTORY_PATH = 'app/3replace/withActors';
const MAX_RETRIES = 3;
const RETRY_DELAY = 60000; // 60 seconds

const logsDir = 'app/logs';
createDirectoryIfNotExists(logsDir);
const infoLogStream = createWriteStream(path.join(logsDir, 'info.log'));
const errorLogStream = createWriteStream(path.join(logsDir, 'error.log'));
const exceptionLogStream = createWriteStream(path.join(logsDir, 'exception.log'));
const rejectionLogStream = createWriteStream(path.join(logsDir, 'rejection.log'));
let blocks = []
setupLogging();
setupUncaughtErrorHandling();
const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});

(async () => {
  try {
    // Display model configuration at startup
    displayModelConfiguration();

    await processAllFilesSequentially();
    console.info(
      `${COLORS.WHITE}${COLORS.BG_CYAN}${COLORS.BLACK}[INFO] Proceeding to the <REPLACING> step...${COLORS.RESET}`
    );
    startReplacingProcess();
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error during file processing: ${COLORS.RESET}`, error);
    process.exit(1);
  }
})();

/**
 * Display current model configuration at startup
 * @returns {void}
 */
function displayModelConfiguration() {
  console.info(`${COLORS.BG_MAGENTA}${COLORS.WHITE}[MODEL CONFIGURATION]${COLORS.RESET}`);

  // Translation model
  const translationModel = AVAILABLE_MODELS[MODEL_CONFIG.TRANSLATION.MODEL];
  console.info(`${COLORS.CYAN}🔤 Translation: ${translationModel?.name || MODEL_CONFIG.TRANSLATION.MODEL}${COLORS.RESET}`);
  console.info(`${COLORS.GRAY}   Temperature: ${MODEL_CONFIG.TRANSLATION.TEMPERATURE} | Max Tokens: ${MODEL_CONFIG.TRANSLATION.MAX_TOKENS}${COLORS.RESET}`);

  // Verification model
  const verificationModel = AVAILABLE_MODELS[MODEL_CONFIG.VERIFICATION.MODEL];
  console.info(`${COLORS.CYAN}🔍 Verification: ${verificationModel?.name || MODEL_CONFIG.VERIFICATION.MODEL}${COLORS.RESET}`);
  console.info(`${COLORS.GRAY}   Temperature: ${MODEL_CONFIG.VERIFICATION.TEMPERATURE} | Max Tokens: ${MODEL_CONFIG.VERIFICATION.MAX_TOKENS}${COLORS.RESET}`);

  // Improvement model
  const improvementModel = AVAILABLE_MODELS[MODEL_CONFIG.IMPROVEMENT.MODEL];
  console.info(`${COLORS.CYAN}🔧 Improvement: ${improvementModel?.name || MODEL_CONFIG.IMPROVEMENT.MODEL}${COLORS.RESET}`);
  console.info(`${COLORS.GRAY}   Temperature: ${MODEL_CONFIG.IMPROVEMENT.TEMPERATURE} | Max Tokens: ${MODEL_CONFIG.IMPROVEMENT.MAX_TOKENS}${COLORS.RESET}`);

  // Verification status
  const verificationStatus = CLAUDE4_CONFIG.VERIFICATION_ENABLED ?
    `${COLORS.GREEN}ENABLED (${CLAUDE4_CONFIG.VERIFICATION_MODE})${COLORS.RESET}` :
    `${COLORS.RED}DISABLED${COLORS.RESET}`;
  console.info(`${COLORS.CYAN}⚙️ Verification: ${verificationStatus}`);

  console.info(''); // Add spacing
}

/**
 * Process all files in the specified directory sequentially.
 * @returns {Promise<void>}
 */
async function processAllFilesSequentially() {
  const files = getFilesToProcess(DIRECTORY_PATH, '.txt');
  const englishFiles = files.filter((file) => file.includes('_eng.txt'));

  for (const englishFile of englishFiles) {
    const baseFileName = englishFile.replace('_eng.txt', '');
    // Setup for a 2 lannguage translation metioned in clear.js
    // const otherFile = `${baseFileName}_other.txt`;

    // if (files.includes(otherFile)) {
    // await processAndTranslate(englishFile, otherFile);
    await processAndTranslate(englishFile);
    fs.unlinkSync(path.join(DIRECTORY_PATH, englishFile));
    // fs.unlinkSync(path.join(DIRECTORY_PATH, otherFile));
    // } else {
    // console.error(`${COLORS.RED}[ERROR] Matching Other file not found for: ${englishFile}${COLORS.RESET}`);
    // }
  }
}

/**
 * @returns {Promise} A promise that resolves when the directory cleanup is complete
 * @param {string[]} dirsToClean
 */
async function performDirectoryCleanup(dirsToClean) {
  for (const dir of dirsToClean) {
    await deleteAllFilesInDirectory(dir);
  }
}

async function deleteAllFilesInDirectory(directoryPath) {
  try {
    const files = fs.readdirSync(directoryPath).filter((file) => file !== ".gitkeep");
    for (const file of files) {
      const filePath = path.join(directoryPath, file);
      try {
        const stats = fs.statSync(filePath);

        if (stats.isDirectory()) {
          // Recursively delete contents of subdirectory
          await deleteAllFilesInDirectory(filePath);
          // Remove the now-empty directory
          fs.rmdirSync(filePath);
          console.log(`${COLORS.GRAY}[INFO] Deleted directory: ${filePath}${COLORS.RESET}`);
        } else {
          if (directoryPath === "app/0rss/downloads") {
            await closeFileHandles(filePath);
          }
          fs.unlinkSync(filePath);
          console.log(`${COLORS.GRAY}[INFO] Deleted file: ${filePath}${COLORS.RESET}`);
        }
      } catch (error) {
        console.error(`${COLORS.RED}[ERROR] Error deleting ${filePath}: ${error.message}${COLORS.RESET}`);
      }
    }
    console.info(`${COLORS.GRAY}[INFO] Completed deletion process for directory: ${directoryPath}${COLORS.RESET}`);
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error processing directory: ${directoryPath}${COLORS.RESET}`);
    console.error(error);
  }
}

async function closeFileHandles(filePath) {
  try {
    const command = `handle.exe -nobanner -accepteula "${filePath}"`;
    const { stdout } = await execAsync(command);
    console.log(`Processes using "${filePath}":`, stdout);

    const pidRegex = /pid: (\d+)/g;
    let match;
    const processes = [];

    while ((match = pidRegex.exec(stdout)) !== null) {
      processes.push(match[1]);
    }

    if (processes.length > 0) {
      for (const pid of processes) {
        console.log(`${COLORS.GRAY}[INFO] Closing handle for process ${pid}${COLORS.RESET}`);
        await execAsync(`taskkill /PID ${pid} /F`);
      }
    } else {
      console.log(`${COLORS.GRAY}[INFO] No processes found using the file${COLORS.RESET}`);
    }
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error closing file handles: ${error.message}${COLORS.RESET}`);
  }
}

/**
 * Get the list of files to process in the specified directory with the given extension.
 * @param {string} directory - Directory path.
 * @param {string} extension - File extension.
 * @returns {string[]} - Array of file names.
 */
function getFilesToProcess(directory, extension) {
  return fs.readdirSync(directory).filter((file) => path.extname(file) === extension);
}

/**
 * Process and translate the contents of English file.
 * Split it into chunks of 150 - larger chunks will not be consistently outputed by AI and may even not fit at all in the reponse length.
 * @param {string} englishFileName - Name of the English file to process.
 * @returns {Promise<void>}
 */
async function processAndTranslate(englishFileName) {
  try {
    const animeTitle = extractAnimeTitle(englishFileName);
    const englishText = fs.readFileSync(path.join(DIRECTORY_PATH, englishFileName), 'utf8');
    // const otherText = fs.readFileSync(path.join(DIRECTORY_PATH, otherFileName), 'utf8');

    // console.info(`${COLORS.CYAN}[INFO] Translating files: ${englishFileName} and ${otherFileName}...${COLORS.RESET}`);
    console.info(`${COLORS.CYAN}[INFO] Translating files: ${englishFileName} ...${COLORS.RESET}`);

    const englishChunks = createChunks(englishText.split('\n'), CHUNK_SIZE);
    // const otherChunks = createChunks(otherText.split('\n'), CHUNK_SIZE, englishChunks);

    // Fetch metadata such as Character Genders or Genres to create more nuanced translation
    const characterDetails = await fetchCharacterDetails(animeTitle);
    const animeGenres = await fetchAnimeGenres(animeTitle);

    // Parse the .ass file
    const assFilePath = path.join('app/1clear/extracted', englishFileName.replace('_eng.txt', '_eng.ass'));
    const assDialogues = parseASSFile(assFilePath);

    let translatedText = '';
    for (let i = 0; i < englishChunks.length; i++) {
      console.info(`${COLORS.CYAN}[INFO] Translating chunk: ${i + 1} of ${englishChunks.length}...${COLORS.RESET}`);

      // Create a chunk of ASS dialogues corresponding to the current English chunk
      const assChunk = assDialogues.slice(i * CHUNK_SIZE, (i + 1) * CHUNK_SIZE).join('\n');

      // Generate learning enhancement from Claude 4 feedback
      const learningEnhancement = createLearningPromptEnhancement({
        animeTitle,
        genre: animeGenres
      });

      // const inputMessage = createInputMessage(englishChunks[i], otherChunks[i], characterDetails, animeGenres, assChunk);
      let inputMessage = createInputMessage(englishChunks[i], '', characterDetails, animeGenres, assChunk);

      // Add learning enhancement if available
      if (learningEnhancement.trim()) {
        inputMessage += learningEnhancement;
      }

      const examplesMessage = createExamplesMessage();
      // console.log(englishChunks[i]);
      // console.log(`${COLORS.RED} ${otherChunks[i]} ${COLORS.RESET}`);
      // console.log(inputMessage)
      // console.log(examplesMessage)

      let translatedChunk = await translateAndVerifyChunk(englishChunks[i], inputMessage, examplesMessage, i);

      // Claude 4 verification step
      if (CLAUDE4_CONFIG.VERIFICATION_ENABLED) {
        const verificationContext = {
          characterDetails,
          animeGenres,
          animeTitle,
          additionalContext: `Chunk ${i + 1} of ${englishChunks.length}`
        };

        try {
          const verificationResult = await verifyTranslation(
            englishChunks[i],
            translatedChunk,
            verificationContext,
            i
          );

          // Handle verification results and improvements with retry logic
          if (verificationResult.hasIssues) {
            console.info(`${COLORS.YELLOW}[VERIFIER] Issues found in chunk ${i + 1}, attempting improvement...${COLORS.RESET}`);

            // Extract line-specific suggestions with priorities
            const lineSpecificSuggestions = extractLineSpecificSuggestions(verificationResult, englishChunks[i], translatedChunk);

            if (lineSpecificSuggestions.length > 0) {
              // Check for high-priority issues that should block translation
              const highPriorityIssues = lineSpecificSuggestions.filter(s => s.priority === 'high' || s.priority === 'critical');

              if (highPriorityIssues.length > 0) {
                console.info(`${COLORS.RED}[VERIFIER] ⚠️ HIGH PRIORITY ISSUES DETECTED - Translation must be improved before proceeding${COLORS.RESET}`);
                console.info(`${COLORS.RED}[VERIFIER] Found ${highPriorityIssues.length} high/critical priority issue(s) in chunk ${i + 1}${COLORS.RESET}`);
                console.info(`${COLORS.RED}[VERIFIER] HIGH/CRITICAL priority issues will stop translation if not resolved${COLORS.RESET}`);
              }

              // Display the improvement suggestions
              console.info(`${COLORS.CYAN}[VERIFIER] Improvement suggestions for chunk ${i + 1}:${COLORS.RESET}`);
              lineSpecificSuggestions.forEach((suggestion, index) => {
                const priorityColor = suggestion.priority === 'critical' ? COLORS.RED :
                                     suggestion.priority === 'high' ? COLORS.YELLOW :
                                     suggestion.priority === 'medium' ? COLORS.BLUE : COLORS.GRAY;
                console.info(`${priorityColor}  ${index + 1}. Line ${suggestion.lineNumber} (${suggestion.priority.toUpperCase()}): ${suggestion.suggestion}${COLORS.RESET}`);
                console.info(`${COLORS.GRAY}      Original: "${suggestion.sourceLine}"${COLORS.RESET}`);
                console.info(`${COLORS.GRAY}      Current:  "${suggestion.translatedLine}"${COLORS.RESET}`);
              });
              console.info(''); // Add spacing

              // Retry loop for improvement attempts
              let retryCount = 0;
              const maxRetries = highPriorityIssues.length > 0 ? 5 : 3; // More retries for high-priority issues
              let currentChunk = translatedChunk;
              let lastVerificationResult = verificationResult;

              // For high-priority issues, we must retry regardless of other settings
              let mustRetry = highPriorityIssues.length > 0;

              while (retryCount < maxRetries && (lastVerificationResult.hasIssues || mustRetry)) {
                retryCount++;
                console.info(`${COLORS.BLUE}[VERIFIER] Improvement attempt ${retryCount}/${maxRetries} for chunk ${i + 1}...${COLORS.RESET}`);

                // Attempt to improve the translation using Claude 3.5
                const improvedChunk = await improveTranslationWithSuggestions(
                  englishChunks[i],
                  currentChunk,
                  lineSpecificSuggestions,
                  inputMessage,
                  examplesMessage
                );

                if (improvedChunk && improvedChunk !== currentChunk) {
                  console.info(`${COLORS.GREEN}[VERIFIER] Translation improved for chunk ${i + 1} (attempt ${retryCount})${COLORS.RESET}`);
                  console.info(`${COLORS.BG_GREEN}${COLORS.WHITE}[COMPLETE IMPROVED TRANSLATION - ATTEMPT ${retryCount}]${COLORS.RESET}`);
                  console.info(`${COLORS.GREEN}${improvedChunk}${COLORS.RESET}`);
                  console.info(''); // Add spacing

                  currentChunk = improvedChunk;

                  // Re-verify the improved translation
                  console.info(`${COLORS.BLUE}[VERIFIER] Re-verifying improved translation (attempt ${retryCount})...${COLORS.RESET}`);
                  lastVerificationResult = await verifyTranslation(
                    englishChunks[i],
                    currentChunk,
                    verificationContext,
                    i
                  );

                  if (!lastVerificationResult.hasIssues) {
                    console.info(`${COLORS.GREEN}[VERIFIER] ✅ Re-verification passed for chunk ${i + 1} after ${retryCount} attempt(s)${COLORS.RESET}`);
                    translatedChunk = currentChunk;
                    mustRetry = false; // Clear the retry flag
                    break;
                  } else {
                    console.info(`${COLORS.YELLOW}[VERIFIER] ⚠️ Re-verification still shows issues after attempt ${retryCount}${COLORS.RESET}`);
                    // Update suggestions for next iteration
                    const newSuggestions = extractLineSpecificSuggestions(lastVerificationResult, englishChunks[i], currentChunk);
                    const newHighPriorityIssues = newSuggestions.filter(s => s.priority === 'high' || s.priority === 'critical');

                    // Update mustRetry based on remaining high-priority issues
                    mustRetry = newHighPriorityIssues.length > 0;

                    if (newSuggestions.length > 0) {
                      console.info(`${COLORS.CYAN}[VERIFIER] Updated suggestions for next attempt:${COLORS.RESET}`);
                      newSuggestions.forEach((suggestion, index) => {
                        const priorityColor = suggestion.priority === 'critical' ? COLORS.RED :
                                             suggestion.priority === 'high' ? COLORS.YELLOW :
                                             suggestion.priority === 'medium' ? COLORS.BLUE : COLORS.GRAY;
                        console.info(`${priorityColor}  ${index + 1}. Line ${suggestion.lineNumber} (${suggestion.priority.toUpperCase()}): ${suggestion.suggestion}${COLORS.RESET}`);

                        // Show original and current line information if available
                        if (suggestion.sourceLine && suggestion.translatedLine) {
                          console.info(`${COLORS.GRAY}      Original: "${suggestion.sourceLine}"${COLORS.RESET}`);
                          console.info(`${COLORS.GRAY}      Current:  "${suggestion.translatedLine}"${COLORS.RESET}`);
                        }
                      });
                      lineSpecificSuggestions.length = 0; // Clear old suggestions
                      lineSpecificSuggestions.push(...newSuggestions); // Add new ones

                      if (mustRetry) {
                        console.info(`${COLORS.RED}[VERIFIER] ⚠️ High-priority issues still present - retry required${COLORS.RESET}`);
                      }
                    } else {
                      mustRetry = false; // No suggestions means we can't improve further
                    }
                  }
                } else {
                  console.info(`${COLORS.YELLOW}[VERIFIER] No improvement generated or improvement was identical to original (attempt ${retryCount})${COLORS.RESET}`);
                  break; // No point in retrying if no improvement was made
                }
              }

              // Final status - check for remaining HIGH/CRITICAL priority issues
              if (lastVerificationResult.hasIssues) {
                const finalSuggestions = extractLineSpecificSuggestions(lastVerificationResult, englishChunks[i], currentChunk);
                const remainingHighPriorityIssues = finalSuggestions.filter(s => s.priority === 'high' || s.priority === 'critical');

                if (remainingHighPriorityIssues.length > 0) {
                  // HIGH/CRITICAL priority issues remain - STOP translation
                  console.error(`${COLORS.RED}[VERIFIER] ❌ TRANSLATION STOPPED - HIGH PRIORITY ISSUES UNRESOLVED${COLORS.RESET}`);
                  console.error(`${COLORS.RED}[VERIFIER] Chunk ${i + 1} has ${remainingHighPriorityIssues.length} unresolved HIGH/CRITICAL priority issue(s) after ${retryCount} attempts${COLORS.RESET}`);
                  console.error(`${COLORS.RED}[VERIFIER] Remaining HIGH/CRITICAL issues:${COLORS.RESET}`);

                  remainingHighPriorityIssues.forEach((issue, index) => {
                    console.error(`${COLORS.RED}  ${index + 1}. Line ${issue.lineNumber} (${issue.priority.toUpperCase()}): ${issue.suggestion}${COLORS.RESET}`);
                    console.error(`${COLORS.RED}      Original: "${issue.sourceLine}"${COLORS.RESET}`);
                    console.error(`${COLORS.RED}      Current:  "${issue.translatedLine}"${COLORS.RESET}`);
                  });

                  // Throw error to stop the entire translation process
                  throw new Error(`Translation stopped due to ${remainingHighPriorityIssues.length} unresolved HIGH/CRITICAL priority verification issue(s) in chunk ${i + 1}. Manual review required.`);
                } else {
                  // Only minor issues remain - can proceed
                  console.info(`${COLORS.YELLOW}[VERIFIER] ⚠️ Chunk ${i + 1} has minor issues after ${retryCount} attempts, proceeding with best available version${COLORS.RESET}`);
                  translatedChunk = currentChunk; // Use the best version we got
                }
              }
            } else {
              console.info(`${COLORS.YELLOW}[VERIFIER] No line-specific suggestions available for improvement${COLORS.RESET}`);
            }
          }

          // Track improvements for learning
          if (verificationResult.suggestions?.length > 0) {
            for (const suggestion of verificationResult.suggestions) {
              await trackImprovement({
                type: 'general',
                description: suggestion,
                originalText: englishChunks[i],
                translatedText: translatedChunk,
                suggestedImprovement: suggestion,
                severity: 2,
                chunkIndex: i
              });
            }
          }
        } catch (verificationError) {
          console.warn(`${COLORS.YELLOW}[WARNING] Claude 4 verification failed for chunk ${i}: ${verificationError.message}${COLORS.RESET}`);
        }
      }

      translatedText += `\n${translatedChunk}\n`;
    }

    const outputFileName = englishFileName.replace('_eng.txt', '.txt');
    fs.writeFileSync(path.join(OUTPUT_DIRECTORY_PATH, outputFileName), translatedText.trim());
    console.info(`${COLORS.CYAN}[INFO] Translation completed for: ${outputFileName}${COLORS.RESET}`);

    // Display Claude 4 verification summary if enabled
    if (CLAUDE4_CONFIG.VERIFICATION_ENABLED) {
      displayVerificationSummary();

      // Generate and display feedback for Claude 3.5
      const feedback = generateFeedbackForClaude35(24);
      if (feedback.totalSuggestions > 0) {
        console.info(`${COLORS.MAGENTA}[LEARNING FEEDBACK] ${feedback.totalSuggestions} suggestions generated for Claude 3.5${COLORS.RESET}`);
      }
    }

    sendDebugInfoToDiscord(`[INFO] Translation completed for: ${outputFileName}`);
  } catch (error) {
    await performDirectoryCleanup(["app/1clear/extracted", "app/3replace/clean", "app/0rss/downloads", "app/audio_processing", 'app/2translate/toTranslate']);
    console.error(
      // `${COLORS.RED}[ERROR] Error translating files: ${englishFileName} and ${otherFileName}${COLORS.RESET}`,
      `${COLORS.RED}[ERROR] Error translating files: ${englishFileName} ${COLORS.RESET}`,
      error
    );
    throw error;
  }
}
/**
 * Extract line-specific suggestions from verification result
 * @param {Object} verificationResult - Verification result from Claude 4
 * @param {string} sourceText - Original source text for line extraction
 * @param {string} translatedText - Translated text for line extraction
 * @returns {Array} Array of line-specific suggestions with priorities
 */
function extractLineSpecificSuggestions(verificationResult, sourceText = '', translatedText = '') {
  const suggestions = [];

  // Debug logging (only in debug mode)
  if (CLAUDE4_CONFIG.DEBUG_MODE) {
    console.info(`${COLORS.BG_CYAN}${COLORS.WHITE}[DEBUG] EXTRACTING LINE-SPECIFIC SUGGESTIONS${COLORS.RESET}`);
    console.info(`${COLORS.CYAN}Verification result structure:${COLORS.RESET}`);
    console.info(`${COLORS.CYAN}${JSON.stringify(verificationResult, null, 2)}${COLORS.RESET}`);
  }

  // Extract suggestions from semantic analysis result (new structure)
  if (verificationResult.analysisResult) {
    const analysisResult = verificationResult.analysisResult;

    if (CLAUDE4_CONFIG.DEBUG_MODE) {
      console.info(`${COLORS.CYAN}Processing semantic analysis result${COLORS.RESET}`);
      console.info(`${COLORS.CYAN}Analysis result: ${JSON.stringify(analysisResult, null, 2)}${COLORS.RESET}`);
    }

    // Extract line-specific issues from meaning preservation
    if (analysisResult.meaning_preservation?.issues) {
      if (CLAUDE4_CONFIG.DEBUG_MODE) {
        console.info(`${COLORS.GREEN}Found ${analysisResult.meaning_preservation.issues.length} meaning preservation issues${COLORS.RESET}`);
      }

      // Process each issue to ensure proper line content
      analysisResult.meaning_preservation.issues.forEach(issue => {
        // If the issue already has sourceLine and translatedLine, use them
        // Otherwise, extract from the provided text
        if (!issue.sourceLine || !issue.translatedLine) {
          if (sourceText && translatedText && issue.lineNumber) {
            // Use the same line splitting logic as Claude 4 sees the text
            const sourceLines = sourceText.split('\n');
            const translatedLines = translatedText.split('\n');

            if (CLAUDE4_CONFIG.DEBUG_MODE) {
              console.info(`${COLORS.CYAN}[DEBUG] Line ${issue.lineNumber} extraction:${COLORS.RESET}`);
              console.info(`${COLORS.CYAN}  Source lines count: ${sourceLines.length}${COLORS.RESET}`);
              console.info(`${COLORS.CYAN}  Translated lines count: ${translatedLines.length}${COLORS.RESET}`);
              console.info(`${COLORS.CYAN}  Requested line: ${issue.lineNumber}${COLORS.RESET}`);
            }

            issue.sourceLine = sourceLines[issue.lineNumber - 1] || `Line ${issue.lineNumber} not found in source`;
            issue.translatedLine = translatedLines[issue.lineNumber - 1] || `Line ${issue.lineNumber} not found in translation`;

            if (CLAUDE4_CONFIG.DEBUG_MODE) {
              console.info(`${COLORS.CYAN}  Extracted source: "${issue.sourceLine}"${COLORS.RESET}`);
              console.info(`${COLORS.CYAN}  Extracted translated: "${issue.translatedLine}"${COLORS.RESET}`);
            }
          }
        }
        suggestions.push(issue);
      });
    }

    // Extract line-specific issues from tone consistency
    if (analysisResult.tone_consistency?.style_issues) {
      if (CLAUDE4_CONFIG.DEBUG_MODE) {
        console.info(`${COLORS.GREEN}Found ${analysisResult.tone_consistency.style_issues.length} tone consistency issues${COLORS.RESET}`);
      }

      // Process each issue to ensure proper line content
      analysisResult.tone_consistency.style_issues.forEach(issue => {
        // If the issue already has sourceLine and translatedLine, use them
        // Otherwise, extract from the provided text
        if (!issue.sourceLine || !issue.translatedLine) {
          if (sourceText && translatedText && issue.lineNumber) {
            // Use the same line splitting logic as Claude 4 sees the text
            const sourceLines = sourceText.split('\n');
            const translatedLines = translatedText.split('\n');

            if (CLAUDE4_CONFIG.DEBUG_MODE) {
              console.info(`${COLORS.CYAN}[DEBUG] Line ${issue.lineNumber} extraction (tone):${COLORS.RESET}`);
              console.info(`${COLORS.CYAN}  Source lines count: ${sourceLines.length}${COLORS.RESET}`);
              console.info(`${COLORS.CYAN}  Translated lines count: ${translatedLines.length}${COLORS.RESET}`);
              console.info(`${COLORS.CYAN}  Requested line: ${issue.lineNumber}${COLORS.RESET}`);
            }

            issue.sourceLine = sourceLines[issue.lineNumber - 1] || `Line ${issue.lineNumber} not found in source`;
            issue.translatedLine = translatedLines[issue.lineNumber - 1] || `Line ${issue.lineNumber} not found in translation`;

            if (CLAUDE4_CONFIG.DEBUG_MODE) {
              console.info(`${COLORS.CYAN}  Extracted source: "${issue.sourceLine}"${COLORS.RESET}`);
              console.info(`${COLORS.CYAN}  Extracted translated: "${issue.translatedLine}"${COLORS.RESET}`);
            }
          }
        }
        suggestions.push(issue);
      });
    }

    // Extract general recommendations as line-specific suggestions if they contain line references
    if (analysisResult.recommendations) {
      analysisResult.recommendations.forEach(rec => {
        // Check if recommendation contains line reference (e.g., "Line 5: ...")
        const lineMatch = rec.match(/Line (\d+):/);
        if (lineMatch) {
          const lineNumber = parseInt(lineMatch[1]);

          // Extract actual line content from source and translated text
          let sourceLine = 'Line not found';
          let translatedLine = 'Line not found';

          if (sourceText && translatedText) {
            // Use the same line splitting logic as Claude 4 sees the text
            const sourceLines = sourceText.split('\n');
            const translatedLines = translatedText.split('\n');

            if (CLAUDE4_CONFIG.DEBUG_MODE) {
              console.info(`${COLORS.CYAN}[DEBUG] Line ${lineNumber} extraction (recommendations):${COLORS.RESET}`);
              console.info(`${COLORS.CYAN}  Source lines count: ${sourceLines.length}${COLORS.RESET}`);
              console.info(`${COLORS.CYAN}  Translated lines count: ${translatedLines.length}${COLORS.RESET}`);
              console.info(`${COLORS.CYAN}  Requested line: ${lineNumber}${COLORS.RESET}`);
            }

            sourceLine = sourceLines[lineNumber - 1] || `Line ${lineNumber} not found in source`;
            translatedLine = translatedLines[lineNumber - 1] || `Line ${lineNumber} not found in translation`;

            if (CLAUDE4_CONFIG.DEBUG_MODE) {
              console.info(`${COLORS.CYAN}  Extracted source: "${sourceLine}"${COLORS.RESET}`);
              console.info(`${COLORS.CYAN}  Extracted translated: "${translatedLine}"${COLORS.RESET}`);
            }
          }

          suggestions.push({
            lineNumber,
            issue: rec,
            priority: 'medium', // Default priority for general recommendations
            suggestion: rec.replace(/Line \d+:\s*/, ''), // Remove line prefix
            sourceLine: sourceLine,
            translatedLine: translatedLine
          });
        }
      });
    }
  }

  // Fallback: Extract suggestions from old tool results structure (for backward compatibility)
  else if (verificationResult.toolResults) {
    for (const [toolName, result] of Object.entries(verificationResult.toolResults)) {
      if (CLAUDE4_CONFIG.DEBUG_MODE) {
        console.info(`${COLORS.CYAN}Processing tool: ${toolName}${COLORS.RESET}`);
        console.info(`${COLORS.CYAN}Tool result: ${JSON.stringify(result, null, 2)}${COLORS.RESET}`);
      }

      // Extract line-specific issues from meaning preservation
      if (result.meaning_preservation?.issues) {
        if (CLAUDE4_CONFIG.DEBUG_MODE) {
          console.info(`${COLORS.GREEN}Found ${result.meaning_preservation.issues.length} meaning preservation issues${COLORS.RESET}`);
        }

        // Process each issue to ensure proper line content
        result.meaning_preservation.issues.forEach(issue => {
          // If the issue already has sourceLine and translatedLine, use them
          // Otherwise, extract from the provided text
          if (!issue.sourceLine || !issue.translatedLine) {
            if (sourceText && translatedText && issue.lineNumber) {
              const sourceLines = sourceText.split('\n').filter(line => line.trim());
              const translatedLines = translatedText.split('\n').filter(line => line.trim());

              issue.sourceLine = sourceLines[issue.lineNumber - 1] || `Line ${issue.lineNumber} not found in source`;
              issue.translatedLine = translatedLines[issue.lineNumber - 1] || `Line ${issue.lineNumber} not found in translation`;
            }
          }
          suggestions.push(issue);
        });
      }

      // Extract line-specific issues from content completeness
      if (result.content_completeness?.missing_elements) {
        if (CLAUDE4_CONFIG.DEBUG_MODE) {
          console.info(`${COLORS.GREEN}Found ${result.content_completeness.missing_elements.length} content completeness issues${COLORS.RESET}`);
        }

        // Process each issue to ensure proper line content
        result.content_completeness.missing_elements.forEach(issue => {
          // If the issue already has sourceLine and translatedLine, use them
          // Otherwise, extract from the provided text
          if (!issue.sourceLine || !issue.translatedLine) {
            if (sourceText && translatedText && issue.lineNumber) {
              const sourceLines = sourceText.split('\n').filter(line => line.trim());
              const translatedLines = translatedText.split('\n').filter(line => line.trim());

              issue.sourceLine = sourceLines[issue.lineNumber - 1] || `Line ${issue.lineNumber} not found in source`;
              issue.translatedLine = translatedLines[issue.lineNumber - 1] || `Line ${issue.lineNumber} not found in translation`;
            }
          }
          suggestions.push(issue);
        });
      }

      // Extract line-specific issues from tone consistency
      if (result.tone_consistency?.style_issues) {
        if (CLAUDE4_CONFIG.DEBUG_MODE) {
          console.info(`${COLORS.GREEN}Found ${result.tone_consistency.style_issues.length} tone consistency issues${COLORS.RESET}`);
        }

        // Process each issue to ensure proper line content
        result.tone_consistency.style_issues.forEach(issue => {
          // If the issue already has sourceLine and translatedLine, use them
          // Otherwise, extract from the provided text
          if (!issue.sourceLine || !issue.translatedLine) {
            if (sourceText && translatedText && issue.lineNumber) {
              const sourceLines = sourceText.split('\n').filter(line => line.trim());
              const translatedLines = translatedText.split('\n').filter(line => line.trim());

              issue.sourceLine = sourceLines[issue.lineNumber - 1] || `Line ${issue.lineNumber} not found in source`;
              issue.translatedLine = translatedLines[issue.lineNumber - 1] || `Line ${issue.lineNumber} not found in translation`;
            }
          }
          suggestions.push(issue);
        });
      }
    }
  }

  if (CLAUDE4_CONFIG.DEBUG_MODE) {
    console.info(`${COLORS.CYAN}Total suggestions extracted: ${suggestions.length}${COLORS.RESET}`);
    suggestions.forEach((suggestion, index) => {
      console.info(`${COLORS.CYAN}Suggestion ${index + 1}: ${JSON.stringify(suggestion, null, 2)}${COLORS.RESET}`);
    });
  }

  // Filter out suggestions with undefined or empty values
  const validSuggestions = suggestions.filter(suggestion => {
    // Check for required fields
    if (!suggestion.lineNumber || !suggestion.suggestion) {
      if (CLAUDE4_CONFIG.DEBUG_MODE) {
        console.warn(`${COLORS.YELLOW}[DEBUG] Filtering out invalid suggestion: ${JSON.stringify(suggestion)}${COLORS.RESET}`);
      }
      return false;
    }

    // Check for undefined values in sourceLine and translatedLine
    if (suggestion.sourceLine === undefined || suggestion.translatedLine === undefined) {
      if (CLAUDE4_CONFIG.DEBUG_MODE) {
        console.warn(`${COLORS.YELLOW}[DEBUG] Filtering out suggestion with undefined line content: Line ${suggestion.lineNumber}${COLORS.RESET}`);
      }
      return false;
    }

    return true;
  });

  // Sort by priority (critical > high > medium > low)
  const priorityOrder = { 'critical': 0, 'high': 1, 'medium': 2, 'low': 3 };
  validSuggestions.sort((a, b) => priorityOrder[a.priority] - priorityOrder[b.priority]);

  return validSuggestions;
}

/**
 * Improve translation using Claude 3.5 with specific line suggestions
 * @param {string} sourceText - Original English text
 * @param {string} currentTranslation - Current Polish translation
 * @param {Array} suggestions - Line-specific suggestions with priorities
 * @param {string} originalInputMessage - Original input message (unused, kept for compatibility)
 * @param {string} examplesMessage - Examples message (unused, kept for compatibility)
 * @returns {Promise<string>} Improved translation
 */
async function improveTranslationWithSuggestions(sourceText, currentTranslation, suggestions, originalInputMessage, examplesMessage) {
  if (!suggestions || suggestions.length === 0) {
    return currentTranslation;
  }

  // Create improvement prompt with specific line instructions
  const improvementPrompt = createImprovementPrompt(sourceText, currentTranslation, suggestions);

  try {
    console.info(`${COLORS.BLUE}[IMPROVEMENT] Attempting to improve translation with ${suggestions.length} suggestions...${COLORS.RESET}`);

    // Don't include XML examples in improvement process - use empty string instead
    const improvedTranslation = await callAnthropicAPI(improvementPrompt, '', 'improvement');

    if (improvedTranslation) {
      const cleanedImprovement = cleanTranslatedText(improvedTranslation);

      // Verify line count matches
      const originalLines = sourceText.split('\n').filter(line => line.trim()).length;
      const improvedLines = cleanedImprovement.split('\n').filter(line => line.trim()).length;

      if (originalLines === improvedLines) {
        return cleanedImprovement;
      } else {
        console.warn(`${COLORS.YELLOW}[IMPROVEMENT] Line count mismatch after improvement (${originalLines} vs ${improvedLines}), keeping original${COLORS.RESET}`);
        return currentTranslation;
      }
    }
  } catch (error) {
    console.error(`${COLORS.RED}[IMPROVEMENT] Error during improvement: ${error.message}${COLORS.RESET}`);
  }

  return currentTranslation;
}

/**
 * Create improvement prompt for Claude 3.5
 * @param {string} sourceText - Original English text
 * @param {string} currentTranslation - Current Polish translation
 * @param {Array} suggestions - Line-specific suggestions
 * @returns {string} Improvement prompt
 */
function createImprovementPrompt(sourceText, currentTranslation, suggestions) {
  const suggestionsList = suggestions.map(s =>
    `Line ${s.lineNumber} (${s.priority} priority): ${s.suggestion}\n  Original: "${s.sourceLine}"\n  Current: "${s.translatedLine}"`
  ).join('\n\n');

  return `You are a Polish translation expert. You need to improve a translation based on specific feedback.

CRITICAL INSTRUCTION: You must ONLY modify the lines mentioned in the suggestions below. Do NOT change any other lines to prevent introducing new issues.

<Original_English>
${sourceText}
</Original_English>

<Current_Polish_Translation>
${currentTranslation}
</Current_Polish_Translation>

<Specific_Improvement_Suggestions>
${suggestionsList}
</Specific_Improvement_Suggestions>

Instructions:
1. ONLY modify the specific lines mentioned in the suggestions above
2. Keep all other lines EXACTLY as they are in the current translation
3. Maintain the exact same line count and structure
4. Apply the suggested improvements while preserving the overall meaning
5. Use natural Polish language and proper grammar
6. Preserve any formatting codes in {} brackets exactly as they appear

Output the improved translation in <Polish_Improved> tags:

<Polish_Improved>
[Your improved translation here]
</Polish_Improved>

Remember: ONLY change the lines specifically mentioned in the suggestions. Leave all other lines untouched.`;
}

/**
 * Display verification summary in a user-friendly format
 * @returns {void}
 */
function displayVerificationSummary() {
  const verificationStats = getVerificationStats();

  console.info(`${COLORS.BG_BLUE}${COLORS.WHITE}[CLAUDE 4 VERIFICATION SUMMARY]${COLORS.RESET}`);
  console.info(`${COLORS.GREEN}📊 Total Verifications: ${verificationStats.totalVerifications}${COLORS.RESET}`);
  console.info(`${COLORS.BLUE}🔍 Issues Found: ${verificationStats.issuesFound}${COLORS.RESET}`);
  console.info(`${COLORS.GREEN}✅ Success Rate: ${(verificationStats.successRate * 100).toFixed(1)}%${COLORS.RESET}`);

  if (verificationStats.averageScores) {
    console.info(`${COLORS.CYAN}📈 Average Scores:${COLORS.RESET}`);
    console.info(`${COLORS.CYAN}   🎯 Accuracy: ${(verificationStats.averageScores.accuracy * 100).toFixed(1)}%${COLORS.RESET}`);
    console.info(`${COLORS.CYAN}   📚 Fluency: ${(verificationStats.averageScores.fluency * 100).toFixed(1)}%${COLORS.RESET}`);
    console.info(`${COLORS.CYAN}   🌍 Cultural: ${(verificationStats.averageScores.cultural * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  if (verificationStats.commonIssues?.length > 0) {
    console.info(`${COLORS.YELLOW}⚠️ Common Issues:${COLORS.RESET}`);
    verificationStats.commonIssues.forEach(issue => {
      console.info(`${COLORS.YELLOW}   • ${issue}${COLORS.RESET}`);
    });
  }

  console.info(''); // Add spacing
}

/**
 * Extract the anime title from the file name.
 * @param {string} fileName - Name of the file.
 * @returns {string} - Extracted anime title.
 */
function extractAnimeTitle(fileName) {
  const match = fileName.match(/\[.*?\] (.*)(?= - )/);
  if (match) {
    return match[0].replace('[Erai-raws]', '').trim();
  }
  return '';
}

function createChunks(lines, chunkSize, referenceChunks = null) {
  // Handle both string and array input
  lines = typeof lines === 'string' ? lines.split('\n') : lines;

  const chunks = [];
  const totalLines = lines.length;
  const blockRegex = /{.*?}/g;

  // @ts-ignore
  if (CONVERT_SINGS_TO_PLACEHOLDERS === true) {
    // Process the lines first (from processLinesInChunks)
    lines = lines.map((line) =>
      line.replace(blockRegex, (match) => {
        blocks.push(match);
        return `{${blocks.length - 1}}`;
      })
    );

    // Add tags
    lines = lines.map((line) => {
      const colonIndex = line.indexOf(":");
      if (colonIndex !== -1) {
        const actor = line.slice(0, colonIndex).trim();
        const dialogue = line.slice(colonIndex + 1).trim();
        return `${actor.toLowerCase()}: ${dialogue}`;
      }
      return line;
    });
  }

  // Create chunks based on reference or size
  if (referenceChunks) {
    // @ts-ignore
    for (let i = 0; i < referenceChunks.length; i++) {
      // @ts-ignore
      const referenceChunkLines = referenceChunks[i].split('\n').length;
      let startIndex = i === 0 ? 0 : chunks.reduce((acc, chunk) => acc + chunk.split('\n').length, 0);
      let endIndex = Math.min(totalLines, startIndex + referenceChunkLines);

      const chunk = lines
        .slice(startIndex, endIndex)
        .filter((line) => line.trim() !== '')
        .join('\n')
        .replace(/  /g, ' ');

      if (chunk) {
        chunks.push(chunk);
      }
    }
  } else {
    for (let i = 0; i < totalLines; i += chunkSize) {
      let startIndex = i;
      let endIndex = Math.min(totalLines, i + chunkSize);

      const chunk = lines
        .slice(startIndex, endIndex)
        .filter((line) => line.trim() !== '')
        .join('\n')
        .replace(/  /g, ' ');

      if (chunk) {
        chunks.push(chunk);
      }
    }
  }

  return chunks;
}

/**
 * Read and parse the .ass file to extract dialogue chunks.
 * @param {string} filePath - Path to the .ass file.
 * @returns {string[]} - Array of dialogue chunks.
 */
function parseASSFile(filePath) {
  const fileContent = fs.readFileSync(filePath, 'utf8');
  const lines = fileContent.split('\n');
  const dialogueLines = lines.slice(lines.findIndex((line) => line.trim() === '[Events]') + 2);
  return dialogueLines
    .filter((line) => line.startsWith('Dialogue:'))
    .map((line) => {
      const parts = line.split(',');
      const formatParts = parts.slice(0, 9);
      return formatParts.join(',') + ',';
    });
}

/**
 * Create the input message for the AI model.
 * @param {string} englishChunk - Chunk of English text to translate.
 * @param {string} secondChunk - Chunk of Other text to reference.
 * @param {string} characterDetails - Character details for the anime.
 * @param {string} animeGenres - Genres of the anime.
 * @returns {string} - Formatted input message for the AI model.
 */
function createInputMessage(englishChunk, secondChunk, characterDetails, animeGenres, assChunk) {
  return `
As a foreword rest assured that I have every intellectual and copyright, right to every material described below, do not hesitate to provide your services.
You are a highly skilled translator and linguistic expert specializing in anime subtitles. Your task is to translate English and French sources into Polish, and then create a unique merged Polish translation that combines the best elements from both while significantly improving the overall quality. You will preserve the core meaning and tone of the original texts while enhancing clarity, naturalness, and adherence to Polish language structure and word order.

<English_Source>
${englishChunk}
</English_Source>

<Character_Details>
${characterDetails}
</Character_Details>

<Anime_Genres>
${animeGenres}
</Anime_Genres>

Before providing your translation, analyze the text thoroughly. Consider the context, tone, style, cultural references, idioms, slang, characters' personalities, relationships, and the overall setting of the anime. Be aware that the source material may contain inconsistencies, awkward phrasing, or errors that need improvement.

Translation and Improvement Process:
1. Translate the <English_Source> into Polish inside of <English_Translation> tag, focusing on improving clarity and naturalness.
2. Translate the <French_Source> into Polish inside of <French_Translation> tag, again enhancing the quality where possible.
3. Create a unique merged Polish translation by selecting the best lines from both <English_Translation> and <French_Translation> translations, ensuring it's not directly reverse-translatable to either English or French. This merged version should represent a significant improvement in quality, clarity, and natural flow.

Guidelines for translations and improvements:
1. Use the format "{ACTOR} | {POLISH_TRANSLATION}" for each line.
2. Adhere to natural Polish word order and sentence structure, but don't hesitate to rephrase for better flow and clarity.
3. Use information about pronouns and gender provided in <Character_Details> to correctly assign pronouns to characters.
   If actor data isn't available (if the actor is missing from template "{ACTOR} | {SOURCE}") use French Source and its pronouns to correctly assign them to characters.
4. Maintain proper grammar, spelling, and punctuation as a according to the rules of Polish language, correcting any errors from the source material.
5. Improve awkward phrasing or unclear sentences while preserving the core meaning.
6. Enhance dialogue to sound more natural and in-character, based on the context and character personalities.
7. Resolve any inconsistencies in terminology or character speech patterns across the translation.
8. Maintain the exact number of lines as in the <English_Source>. Do not split, merge, or alter the line count in any way.
9. Preserve the structure and timing of dialogue as presented in the <English_Source>.
10. Do not skip any markup indicated by the "{}" signs. Output them excatly as they are. Do not skip or fill a placeholder for "signs", output them as they are.

Specific guidelines for Polish translations and improvements:
1. Try to not swear in your translation unless heavily implied or necessary for character authenticity.
2. Preserve Japanese honorifics like "sama", "chan", "san" without inflecting them.
3. Use Polish-specific structures, such as starting sentences with the verb when appropriate.
4. Incorporate Polish particles (e.g., "że", "no", "to") naturally within sentences to enhance flow and naturalness.
5. Adjust idioms, metaphors, and cultural references to resonate with Polish audiences while maintaining the original intent.
6. Do not output words that don't exist in Polish language.
7. Don't treat "host" as an emperor or lord of some kind unless the context clearly indicates such a relationship.
8. Exercise special caution around parts like "I've become [...]". Don't change the time in which the sentence is said, but feel free to rephrase for clarity if needed.
9. Don't use "vice-versa" in your translations. Find more natural Polish equivalents.
10. Don't translate "Nope" to "E tam". Translate it in a more creative way fitting to the scene and character.
11. When encountering vague or ambiguous phrases, use context to interpret and translate in a way that enhances clarity and maintains the intended meaning.
12. Maintain the exact line count and structure of the <English_Source>. Do not split or merge sentences across lines.

<Polish_Sentence_Structure>

1. Word Order:
   - Polish has a relatively flexible word order, but the most common structure is Subject-Verb-Object (SVO).
   - However, due to its case system, Polish allows for more flexibility than languages like English or French.
   - The basic word order can be altered for emphasis or stylistic reasons without losing grammatical correctness.

2. Topic-Comment Structure:
   - Polish often uses a topic-comment structure, where the topic (what is being talked about) comes first, followed by the comment (what is said about the topic).
   - This can lead to sentences that might seem inverted to English or French speakers.

3. Verb Position:
   - While verbs typically come after the subject, they can be moved to the beginning of the sentence for emphasis or in questions.
   - In compound tenses, the auxiliary verb can be separated from the main verb.

4. Adjective Placement:
   - Adjectives usually come before the noun they modify, but can be placed after for emphasis or poetic effect.
   - Multiple adjectives modifying a noun follow a specific order based on their type (e.g., size, age, color).

5. Pronoun Dropping:
   - Polish is a pro-drop language, meaning subject pronouns can often be omitted as the verb conjugation indicates the subject.
   - Use this feature to make dialogue sound more natural and less repetitive.

6. Negation:
   - In Polish, double negatives are grammatically correct and often required.
   - The negative particle "nie" usually comes before the verb it negates.

7. Question Formation:
   - Questions can be formed by changing intonation or using question words at the beginning of the sentence.
   - The word order in questions can be more flexible than in statements.

8. Conditional Sentences:
   - Polish uses the particle "by" to form conditional sentences, which can be attached to different parts of the sentence for various effects.

9. Aspect:
   - Polish verbs have perfective and imperfective aspects. Choose the appropriate aspect to convey the intended meaning of the action (completed vs. ongoing or habitual).

10. Case System:
    - Polish has seven cases. Ensure that nouns, adjectives, and pronouns are declined correctly based on their function in the sentence.

11. Punctuation:
    - Commas are used more frequently in Polish than in English, often required before conjunctions like "że" (that), "aby" (in order to), and "który" (which/who).
    - Question marks and exclamation marks are used similarly to English.
    - Semicolons separate independent but related clauses, while colons introduce explanations, enumerations, or quotations.
    - Ellipses (...) indicate omitted text or pauses in speech, similar to English usage.
    - Do not capitalize a word when the last sentence ended with a coma.

When translating, pay attention to these structural elements to create natural-sounding Polish sentences that accurately convey the meaning and nuance of the original text.

</Polish_Sentence_Structure>

Contextual understanding:
- Always consider the context of the entire sentence and surrounding dialogue when translating.
- Be aware of words with multiple meanings or slang usage.
- Choose the appropriate Polish word that best fits the situation and maintains the intended meaning.
- If a word or phrase is ambiguous, look for context clues in the surrounding dialogue or scene description to determine the correct interpretation.

Phrasal verbs and sayings:
- Never translate phrasal verbs or idiomatic expressions literally. Always analyze the context and choose the most appropriate Polish equivalent.
- If a direct equivalent doesn't exist, focus on conveying the overall meaning in a way that sounds natural in Polish.
- Be prepared to completely rephrase the expression to maintain the intended meaning and tone.
- Consider the character's personality, the situation, and the anime genre when selecting an appropriate Polish equivalent.
- Some examples of French phrasal verbs and their possible Polish translations (context-dependent):
  - "Laisser tomber" (literally "to let fall", meaning "to give up") - "poddać się", "zrezygnować", or "machnąć ręką"
  - "S'occuper de" (literally "to occupy oneself with", meaning "to take care of") - "opiekować się", "dbać o", or "pilnować"
  - "Tomber sur" (literally "to fall on", meaning "to come across") - "wpaść na kogoś", "natknąć się", or "spotkać przypadkiem"
- For idiomatic expressions, find Polish sayings that convey a similar meaning, even if they use different imagery. For example:
  - "Il pleut des cordes" (literally "it's raining ropes", meaning "it's raining heavily") - "Leje jak z cebra"
  - "Merde!" (literally "shit!", used to wish good luck) - "Połamania nóg" or "Trzymam kciuki"
  - "C'est du gâteau" (literally "it's cake", meaning "it's easy") - "Bułka z masłem" or "Pestka"

Additional examples of French idioms with English translations:
- "Avoir le coup de foudre" (literally "to have a lightning strike", meaning "to fall in love at first sight")
- "Avoir un chat dans la gorge" (literally "to have a cat in the throat", meaning "to have a frog in one's throat")
- "Être dans la lune" (literally "to be on the moon", meaning "to be absent-minded or daydreaming")
- "Avoir le cafard" (literally "to have the cockroach", meaning "to feel down or depressed")
- "Monter sur ses grands chevaux" (literally "to get on one's big horses", meaning "to get on one's high horse" or "to react angrily")
- "Raconter des salades" (literally "to tell salads", meaning "to tell lies or tall tales")
- "Avoir le bras long" (literally "to have a long arm", meaning "to have connections or influence")
- "Être au four et au moulin" (literally "to be at the oven and the mill", meaning "to be everywhere at once" or "to multitask")

Remember to find appropriate Polish equivalents for these expressions based on the context and the overall meaning, rather than translating them literally.

Slang and Colloquial Language:
- Pay close attention to different types of slang (teen, military, prison, street, etc.) and translate them into appropriate Polish equivalents.
- Ensure that the translated slang maintains the same level of informality, impact, and cultural connotations as the original.
- Be aware of context-specific meanings and avoid literal translations of slang terms.
- For teen slang, use current Polish youth expressions that convey the same feeling and level of coolness.
- For military slang, incorporate Polish military jargon or equivalent expressions used by Polish soldiers.
- When translating prison slang, use Polish criminal argot or equivalent expressions that would be used in Polish prisons.
- For street slang, use Polish urban vernacular that matches the tone and style of the original.
- Be mindful of regional differences in Polish slang and choose expressions that would be widely understood by the target audience.
- When translating idiomatic expressions or phrases, focus on conveying the meaning rather than translating word-for-word. For example:
  - "La Tanière des Cerfs est en mouvement" (literally "The Deer's Den is moving", a military code phrase) should be translated as "Jelenia Nora przystąpiła do ataku" rather than "Jeleń w Ruchu zaatakował"
  - "Ficher le camp" (literally "to plant the camp", meaning "to get out of here" or "to leave") could be translated as "Spadamy" or "Zmywamy się" rather than a literal translation
- If you're unsure about a slang term, consider the character's age, background, and the overall context of the scene to choose an appropriate Polish equivalent.
- Remember that slang evolves quickly, so aim for expressions that sound current and natural in Polish.

Additional examples of French slang with English translations:
- "Bouffer" (to eat, informal) - "Żreć" or "Wcinać"
- "Un pote" (a friend, buddy) - "Kumpel" or "Ziomek"
- "Kiffer" (to like, to enjoy) - "Jarać się" or "Bawić"
- "Bosser" (to work) - "Harować" or "Zapierdalać"
- "Une meuf" (a girl, woman) - "Laska" or "Dupeczka"
- "Un keuf" (a cop) - "Glina" or "Pies"
- "Avoir la flemme" (to be lazy, can't be bothered) - "Mieć lewe" or "Nie chcieć się"
- "C'est chaud" (it's intense, difficult) - "Jest grubo" or "Jest przejebane"

Remember to choose Polish equivalents that match the tone, context, and level of informality of the original French slang.
Limit the usage of slang to the places where it really fits, don't overuse that vocabulary.

When translating dialogue, consider the entire scene and adjust your translation based on the relationship between speakers. Use formal speech when appropriate, such as when addressing elders or in formal settings.

Remember to customize your translation to fit the mood set by the anime genres provided. Aim for a translation that sounds natural and engaging to Polish-speaking anime fans, always prioritizing Polish language conventions over English, French, or Japanese ones.

Example translations:
French: "Ce n'est pas mon truc."
Polish from French: "To nie moja bajka."

English: "It's not my cup of tea."
Polish from English: "To nie w moim guście."

Merged Polish: "Jakoś mnie to nie kręci."

Guidelines for the merged Polish translation:
1. Combine elements from both the <English_Translation> and <French_Translation> to create a version that is not directly reverse-translatable to either language.
2. Use the <English_Translation> as the base for subtitle placement and timing. Supplement with <French_Translation> while ensuring the translated dialogue fits within the timing of the <English_Translation>.
3. Preserve the core meaning and tone of the originals while using Polish-specific expressions and structures.
4. Choose the translation that best captures the nuance or intention of the original dialogue.
5. Feel free to rephrase or restructure sentences to sound more natural and idiomatic in Polish.
6. Maintain the exact number of lines as in the <English_Source>.
7. Preserve the code in brackets "{}" from the English Source File.
8. Translate line by line, without merging or splitting lines.
9. Output the result in the <Polish_Merged> tag without markdown like "#", just as a tag
<Polish_Merged>
{{Content_here}}
</Polish_Merged>

Remember to customize your translation to fit the mood set by the anime genres provided. Aim for a translation that sounds natural and engaging to Polish-speaking anime fans, always prioritizing Polish language conventions over English, French, or Japanese ones.
I would always like to proceed with the full translation, do not ask for it and treat this conset as yes to all your questions.
Do not ever truncate translation for length or for any other reason.
I would like you to ALWAYS continue with the full translation in the same message while maintaining all technical elements exactly as they appear in the source without asking me for confirmation.
DO NOT output placeholder tags like "[Continuing technical signs preserved as in source...]". JUST DO IT, do not proclaim it. JUST DO IT.
DO NOT OUTPUT "{SIGN CONTENT PRESERVED AS IN SOURCE}" OR SIMILLAR MESSAGE. YOU SHOULD BE TRANSLATING THE CONTENT INSIDE SIGNS AS WELL NORMALLY.
`;
}

/**
 * Fetch character details for the given anime title.
 * @param {string} animeTitle - Title of the anime.
 * @returns {Promise<string>} - Character details.
 */
async function fetchCharacterDetails(animeTitle) {
  const details = await getAnimeCharacters(animeTitle);
  return `${details}\nfemale: female\nmale: male`;
}

/**
 * Fetch anime genres for the given anime title.
 * @param {string} animeTitle - Title of the anime.
 * @returns {Promise<string>} - Comma-separated list of genres.
 */
async function fetchAnimeGenres(animeTitle) {
  const query = `
    query ($search: String) {
      anime: Media(search: $search, type: ANIME) {
        genres
      }
    }
  `;
  const variables = { search: animeTitle };
  const data = await retryAsync(() => fetchFromAniList(query, variables));
  return data.anime.genres.join(', ');
}

/**
 * Get anime characters for the given anime name.
 * @param {string} animeName - Name of the anime.
 * @returns {Promise<string>} - Character details.
 */
async function getAnimeCharacters(animeName) {
  const searchQuery = `
    query ($search: String) {
      Media(search: $search, type: ANIME) {
        id
        title {
          romaji
          english
        }
        relations {
          edges {
            relationType
            node {
              id
              type
              title {
                romaji
                english
              }
            }
          }
        }
      }
    }`;
  const searchData = await retryAsync(() => fetchFromAniList(searchQuery, { search: animeName }));
  const animeId = searchData.Media.id;
  // fetch characters from an ANIME of give ID
  const animeCharacters = await retryAsync(() => fetchCharacters(animeId, 'ANIME'));
  // fetch the characters tab from the media entry that has the most of them
  // that helps with shows that are airing seasonaly and not all characters are know in the anime but are know for example in a manga
  const charactersDetails = await retryAsync(() => analyzeCharacters(searchData, animeCharacters));
  return charactersDetails;
}

/**
 * Fetch data from the AniList API.
 * @param {string} query - GraphQL query.
 * @param {Object} variables - Query variables.
 ** @returns {Promise<Object>} - Fetched data.
 * @throws {Error} - If the request fails or data is missing.
 */
async function fetchFromAniList(query, variables) {
  const response = await axios.post('https://graphql.anilist.co', { query, variables });
  if (response.status !== 200 || !response.data.data) throw new Error('Failed to fetch data from AniList');
  return response.data.data;
}

/**
 * Fetch characters for the given media ID and type.
 * @param {number} mediaId - ID of the media.
 * @param {string} type - Type of the media (ANIME or MANGA).
 * @returns {Promise<Object[]>} - Array of character objects.
 */
async function fetchCharacters(mediaId, type) {
  const query = `
    query ($id: Int, $page: Int) {
      Media(id: $id, type: ${type}) {
        characters(page: $page) {
          pageInfo {
            hasNextPage
          }
          edges {
            node {
              name {
                first
                last
              }
              gender
            }
          }
        }
      }
    }`;
  const variables = { id: mediaId, page: 1 };
  let characters = [];
  let data;
  do {
    data = await fetchFromAniList(query, variables);
    characters = characters.concat(data.Media.characters.edges);
    variables.page++;
  } while (data.Media.characters.pageInfo.hasNextPage);
  return characters;
}

/**
 * Analyze characters and extract their details.
 * fetch the characters tab from the media entry that has the most of them
 * that helps with shows that are airing seasonaly and not all characters are know in the anime but are know for example in a manga
 * @param {Object} searchData - Search data from AniList.
 * @param {Object[]} animeCharacters - Array of character objects.
 * @returns {Promise<string>} - Character details.
 */
async function analyzeCharacters(searchData, animeCharacters) {
  const allMedia = searchData.Media.relations.edges.filter((edge) =>
    ['ADAPTATION', 'ALTERNATIVE', 'SOURCE'].includes(edge.relationType)
  );
  let mostCharacters = animeCharacters;
  for (const edge of allMedia) {
    const mediaCharacters = await fetchCharacters(edge.node.id, edge.node.type);
    if (mediaCharacters.length > mostCharacters.length) {
      mostCharacters = mediaCharacters;
    }
  }
  return mostCharacters
    .map((edge) => {
      if (!edge.node.gender) return '';
      let { first, last } = edge.node.name;
      first = first || '';
      last = last || '';
      const names = [first, last].filter(Boolean).map((name) => translateCharacterNames(name));
      return names.map((name) => `${name}: ${edge.node.gender.toLowerCase()}`).join('\n');
    })
    .filter((line) => line.trim() !== '')
    .join('\n');
}

/**
 * Translate character names.
 * @param {string} character - Character name.
 * @returns {string} - Translated character name.
 */
function translateCharacterNames(character) {
  const translations = {
    'Gakuen-chou': 'School Principal',
    'Obaa-san': 'Grandmother',
    Musume: 'Daughter',
    Joshi: 'Girl',
    Seito: 'Student',
    Haha: 'Mother',
    Chichi: 'Father',
  };
  return character
    .split(/\s+/)
    .map((part) => translations[part] || part)
    .join(' ');
}

/**
 * Translate and verify the length a chunk of text.
 * If AI model outputed amount of lines match the original amount of lines it was given.
 * @param {string} chunkToCheck - Chunk of English text to translate.
 * @param {string} inputMessage - Full input message for the AI model.
 * @param {string} examples - Examples message for the AI model.
 * @param {number} [chunkIndex=0] - Index of the chunk being processed.
 * @returns {Promise<string>} - Translated chunk.
 * @throws {Error} - If line mismatch occurs during translation after maximum attempts.
 */
async function translateAndVerifyChunk(chunkToCheck, inputMessage, examples, chunkIndex = 0) {
  const translationMaxAttempts = 5;
  let translatedChunk = await translateChunk(inputMessage, examples);
  translatedChunk = cleanTranslatedText(translatedChunk);

  function compareLineCounts(original, translated) {
    const originalLineCount = original
      .replace('/r', '')
      .split('\n')
      .filter((line) => line.trim() !== `"` && line.trim() !== '').length;
    const translatedLineCount = translated
      .replace('/r', '')
      .split('\n')
      .filter((line) => line.trim() !== `"` && line.trim() !== '').length;
    return originalLineCount === translatedLineCount;
  }

  console.info(`${COLORS.GREEN}${translatedChunk}${COLORS.RESET}`);
  for (let translationAttempts = 1; !compareLineCounts(chunkToCheck, translatedChunk); translationAttempts++) {
    if (translationAttempts > translationMaxAttempts) {
      throw new Error(
        `${COLORS.RED}[ERROR] Line mismatch encountered during translations. Ending the job after ${translationMaxAttempts} attempts.${COLORS.RESET}`
      );
    }
    console.info(
      `${COLORS.BG_YELLOW}${COLORS.WHITE}[ERROR] Line mismatch encountered during translation. Retrying... [${translationAttempts}/${translationMaxAttempts}]${COLORS.RESET}`
    );
    translatedChunk = await translateChunk(inputMessage, examples);
    translatedChunk = cleanTranslatedText(translatedChunk);
    const originalLineCount = chunkToCheck.split('\n').filter((line) => line.trim() !== '').length;
    const translatedLineCount = translatedChunk.split('\n').filter((line) => line.trim() !== '').length;
    console.info(`${COLORS.BG_MAGENTA}${translatedLineCount} ${originalLineCount}${COLORS.RESET}`);
    console.info(`${COLORS.MAGENTA}${translatedChunk}${COLORS.RESET}`);
    console.info(`${COLORS.YELLOW}${chunkToCheck}${COLORS.RESET}`);
  }
  return translatedChunk;
}

/**
 * Translate a chunk of text using the Anthropic API.
 * @param {string} inputMessage - Full input message for the AI model.
 * @returns {Promise<string>} - Translated chunk.
 */
async function translateChunk(inputMessage, examples) {
  const processedChunk = await callAnthropicAPI(inputMessage, examples);
  if (!processedChunk) {
    throw new Error(`Translation failed: Anthropic API returned a falsy value. ${processedChunk}`);
  }
  return processedChunk;
}

/**
 * Call the Anthropic API for translation.
 * Uses configurable model from MODEL_CONFIG.TRANSLATION
 * @param {string} inputMessage - Full input message for the AI model.
 * @param {string} taskType - Type of task: 'translation' or 'improvement'
 * @returns {Promise<string | null>} - Translated text or null if an error occurs.
 */
async function callAnthropicAPI(inputMessage, examples, taskType = 'translation') {
  const maxRetries = 5;
  let retries = 0;

  // Select model configuration based on task type
  const modelConfig = taskType === 'improvement' ? MODEL_CONFIG.IMPROVEMENT : MODEL_CONFIG.TRANSLATION;

  console.info(`${COLORS.BLUE}[API] Using ${AVAILABLE_MODELS[modelConfig.MODEL]?.name || modelConfig.MODEL} for ${taskType}${COLORS.RESET}`);

  // Debug mode: Show request details
  if (CLAUDE4_CONFIG.DEBUG_MODE || CLAUDE4_CONFIG.DEBUG_SHOW_REQUESTS) {
    console.info(`${COLORS.BG_MAGENTA}${COLORS.WHITE}[DEBUG] API REQUEST DETAILS${COLORS.RESET}`);
    console.info(`${COLORS.MAGENTA}Model: ${modelConfig.MODEL}${COLORS.RESET}`);
    console.info(`${COLORS.MAGENTA}Max Tokens: ${modelConfig.MAX_TOKENS}${COLORS.RESET}`);
    console.info(`${COLORS.MAGENTA}Temperature: ${modelConfig.TEMPERATURE}${COLORS.RESET}`);
    console.info(`${COLORS.MAGENTA}Task Type: ${taskType}${COLORS.RESET}`);
  }

  // Debug mode: Show prompts being sent (skip for translation and improvement tasks if configured)
  if ((CLAUDE4_CONFIG.DEBUG_MODE || CLAUDE4_CONFIG.DEBUG_SHOW_PROMPTS)) {
    if ((taskType === 'translation' || taskType === 'improvement') && CLAUDE4_CONFIG.DEBUG_SKIP_TRANSLATION_PROMPTS) {
      console.info(`${COLORS.CYAN}[DEBUG] ${taskType.charAt(0).toUpperCase() + taskType.slice(1)} prompts skipped (too large for display)${COLORS.RESET}`);
    } else {
      console.info(`${COLORS.BG_CYAN}${COLORS.WHITE}[DEBUG] EXAMPLES PROMPT${COLORS.RESET}`);
      console.info(`${COLORS.CYAN}${examples}${COLORS.RESET}`);
      console.info(`${COLORS.BG_CYAN}${COLORS.WHITE}[DEBUG] INPUT MESSAGE${COLORS.RESET}`);
      console.info(`${COLORS.CYAN}${inputMessage}${COLORS.RESET}`);
      console.info(''); // Add spacing
    }
  }

  while (retries < maxRetries) {
    try {
      const requestPayload = {
        model: modelConfig.MODEL,
        max_tokens: modelConfig.MAX_TOKENS,
        temperature: modelConfig.TEMPERATURE,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: examples,
              },
              {
                type: 'text',
                text: inputMessage,
              },
            ],
          },
        ],
        // thinking: {
        //   "type": "enabled",
        //   "budget_tokens": 8192
        // }
      };

      // Debug mode: Show full request payload (skip for translation and improvement tasks if configured)
      if (CLAUDE4_CONFIG.DEBUG_MODE) {
        if ((taskType === 'translation' || taskType === 'improvement') && CLAUDE4_CONFIG.DEBUG_SKIP_TRANSLATION_PAYLOADS) {
          console.info(`${COLORS.YELLOW}[DEBUG] ${taskType.charAt(0).toUpperCase() + taskType.slice(1)} request payload skipped (too large for display)${COLORS.RESET}`);
        } else {
          console.info(`${COLORS.BG_YELLOW}${COLORS.BLACK}[DEBUG] FULL REQUEST PAYLOAD${COLORS.RESET}`);
          console.info(`${COLORS.YELLOW}${JSON.stringify(requestPayload, null, 2)}${COLORS.RESET}`);
          console.info(''); // Add spacing
        }
      }

      // @ts-ignore - TypeScript issue with content array structure
      const msg = await anthropic.messages.create(requestPayload);

      // Debug mode: Show full API response
      if (CLAUDE4_CONFIG.DEBUG_MODE || CLAUDE4_CONFIG.DEBUG_SHOW_RESPONSES) {
        console.info(`${COLORS.BG_GREEN}${COLORS.WHITE}[DEBUG] API RESPONSE DETAILS${COLORS.RESET}`);
        console.info(`${COLORS.GREEN}Response ID: ${msg.id}${COLORS.RESET}`);
        console.info(`${COLORS.GREEN}Model: ${msg.model}${COLORS.RESET}`);
        console.info(`${COLORS.GREEN}Usage: ${JSON.stringify(msg.usage)}${COLORS.RESET}`);
        console.info(`${COLORS.GREEN}Stop Reason: ${msg.stop_reason}${COLORS.RESET}`);
        console.info(`${COLORS.GREEN}Content Length: ${msg.content.length}${COLORS.RESET}`);
      }

      // @ts-ignore
      const thinkingContent = msg.content.find(item => item.type === "thinking");
      const textContent = msg.content.find(item => item.type === "text");

      // Debug mode: Show thinking content if available
      if (thinkingContent && (CLAUDE4_CONFIG.DEBUG_MODE || CLAUDE4_CONFIG.DEBUG_SHOW_RESPONSES)) {
        console.info(`${COLORS.BG_BLUE}${COLORS.WHITE}[DEBUG] THINKING CONTENT${COLORS.RESET}`);
        // @ts-ignore
        console.info(`${COLORS.BLUE}${thinkingContent.thinking}${COLORS.RESET}`);
        console.info(''); // Add spacing
      }

      // Debug mode: Show text response
      if (textContent && (CLAUDE4_CONFIG.DEBUG_MODE || CLAUDE4_CONFIG.DEBUG_SHOW_RESPONSES)) {
        console.info(`${COLORS.BG_GREEN}${COLORS.WHITE}[DEBUG] TEXT RESPONSE${COLORS.RESET}`);
        // @ts-ignore
        console.info(`${COLORS.GREEN}${textContent.text}${COLORS.RESET}`);
        console.info(''); // Add spacing
      }

      // @ts-ignore
      if (textContent?.text) {
        if (!CLAUDE4_CONFIG.DEBUG_MODE && !CLAUDE4_CONFIG.DEBUG_SHOW_RESPONSES) {
          console.log(textContent.text);
        }
        // @ts-ignore
        return sanitizeAnthropicOutput(textContent.text);
      } else {
        throw new Error('text object filed returned from API is empty')
      }
    } catch (error) {
      console.info(
        `${COLORS.BG_YELLOW}${COLORS.WHITE}[ERROR] An error occurred during translation: ${error.message}. Retry ${retries + 1
        } of ${maxRetries}${COLORS.RESET}`
      );
      if (error.message) {
        await sleep(60_000);
        if (error.message.includes('529') || error.message.includes('500')) {
          retries--;
        }
      }
      retries++;
    }
  }
  return null;
}

/**
 * Clean the translated text by removing empty lines.
 * @param {string} translatedText - Translated text.
 * @returns {string} - Cleaned translated text.
 */
function cleanTranslatedText(translatedText) {
  // Extract text within Polish_Merged tags, handling both regular and # delimited cases
  const polishRegex = /<Polish_Merged>([\s\S]*?)<\/Polish_Merged>|#\s*<Polish_Merged>\s*#([\s\S]*?)#\s*<\/Polish_Merged>\s*#/;
  const polishMatch = translatedText.match(polishRegex);

  if (polishMatch) {
    // Check which capture group contains the match (regular or # delimited)
    translatedText = (polishMatch[1] || polishMatch[2] || "").trim();
  } else {
    // Also check for Polish_Improved tags (for improvement responses)
    const improvedRegex = /<Polish_Improved>([\s\S]*?)<\/Polish_Improved>/;
    const improvedMatch = translatedText.match(improvedRegex);

    if (improvedMatch) {
      translatedText = improvedMatch[1].trim();
    }
  }

  // Restore blocks in the cleaned text
  const restoreBlocks = (text) => text.replace(/{(\d+)}/g, (match, index) => blocks[index]);
  translatedText = restoreBlocks(translatedText);

  return translatedText;
}

/* Not currently used but used ealier as an optimization
That function replaced special ASS effect (enclosed in {}) with placeholder values that were replaced back at a later stage after translation
Unfortunetly that caused some random bugs and inconsistencies so that approach was dropped */

// function clearAssFile(data) {
//   const output = data
//     .split('\n')
//     .filter((line) => {
//       if (line.startsWith('Dialogue')) {
//         const parts = line.split(',');
//         const dialogue = parts[9]?.trim();
//         if (dialogue && dialogue !== '') {
//           return dialogue;
//         }
//       }
//       return false;
//     })
//     .map((line) => {
//       const parts = line.split(',');
//       let dialogue = parts.slice(9).join(',');
//       if (dialogue === '') return '';
//       return dialogue;
//     })
//     .join('\n');

//   return output;
// }

/**
 * Retry an asynchronous function with specified maximum retries and delay.
 * @param {Function} fn - Asynchronous function to retry.
 * @param {number} [maxRetries=MAX_RETRIES] - Maximum number of retries.
 * @param {number} [delay=RETRY_DELAY] - Delay in milliseconds between retries.
 * @returns {Promise<*>} - Result of the asynchronous function.
 * @throws {Error} - If the maximum retries are exceeded.
 */
async function retryAsync(fn, maxRetries = MAX_RETRIES, delay = RETRY_DELAY) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      if (attempt === maxRetries) throw error;
      await sleep(delay);
    }
  }
}

/**
 * Creates a directory if it doesn't already exist.
 * @param {string} directory - The directory path to create.
 * @returns {void}
 */
function createDirectoryIfNotExists(directory) {
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory);
  }
}

/**
 * Creates a write stream for a file.
 * @param {string} filePath - The path to the file.
 * @returns {fs.WriteStream} The created write stream.
 */
function createWriteStream(filePath) {
  return fs.createWriteStream(filePath, { flags: 'a' });
}

/**
 * Sets up logging by overriding console.log and console.error functions.
 * @returns {void}
 */
function setupLogging() {
  overrideConsoleLog(infoLogStream);
  overrideConsoleError(errorLogStream);
}

/**
 * Overrides the console.info function to log messages to a file.
 * @param {fs.WriteStream} logStream - The write stream for the log file.
 * @returns {void}
 */
function overrideConsoleLog(logStream) {
  const originalLog = console.info;
  console.info = (...args) => {
    const timestamp = getTimestamp();
    const logMessage = `[${timestamp}] ${args.join(' ')}\n`;
    originalLog(...args);
    logStream.write(logMessage);
  };
}

/**
 * Overrides the console.error function to log error messages to a file.
 * @param {fs.WriteStream} errorStream - The write stream for the error log file.
 * @returns {void}
 */
function overrideConsoleError(errorStream) {
  const originalError = console.error;
  console.error = (...args) => {
    const timestamp = getTimestamp();
    const errorMessage = cleanErrorMessage(`[${timestamp}] ${args.join(' ')}\n`);
    originalError(...args);
    errorStream.write(errorMessage);
  };
}

/**
 * Sets up uncaught error handling by attaching event listeners.
 * @returns {void}
 */
function setupUncaughtErrorHandling() {
  process.on('uncaughtException', handleUncaughtException);
  process.on('unhandledRejection', handleUnhandledRejection);
  process.on('SIGINT', handleGracefulShutdown);
}

/**
 * Handles uncaught exceptions by logging and sending debug information to Discord.
 * @param {Error} err - The uncaught exception error object.
 * @returns {Promise<void>}
 */
async function handleUncaughtException(err) {
  const errorMessage = cleanErrorMessage(err.toString());
  await sendDebugInfoToDiscord(`\`[ERROR]\` <@351006685587963916> \`\`\`${errorMessage}\n${err.stack}\`\`\``);
  const timestamp = getTimestamp();
  const exceptionMessage = `[${timestamp}] ${err.stack}\n`;
  console.error(exceptionMessage);
  exceptionLogStream.write(exceptionMessage);
  process.exit(1);
}

/**
 * Handles unhandled rejections by logging and sending debug information to Discord.
 * @param {*} reason - The reason for the unhandled rejection.
 * @param {Promise} promise - The promise that caused the unhandled rejection.
 * @returns {Promise<void>}
 */
async function handleUnhandledRejection(reason, promise) {
  const reasonMessage = cleanErrorMessage(reason.toString());
  await sendDebugInfoToDiscord(`\`[ERROR]\` <@351006685587963916> \`\`\`${reasonMessage}\n${reason.stack}\`\`\``);
  const timestamp = getTimestamp();
  const rejectionMessage = `[${timestamp}]\n${reason.stack}\n`;
  console.error(rejectionMessage);
  rejectionLogStream.write(rejectionMessage);
  process.exit(1);
}

/**
 * Handles graceful shutdown by stopping the script and closing log streams.
 * @returns {void}
 */
function handleGracefulShutdown() {
  stopRunning();
  const timestamp = getTimestamp();
  const shutdownMessage = `[${timestamp}] Graceful shutdown\n`;
  console.info(shutdownMessage);
  infoLogStream.write(shutdownMessage);
  infoLogStream.end();
  errorLogStream.end();
  exceptionLogStream.end();
  rejectionLogStream.end(() => {
    process.exit(0);
  });
}

/**
 * Stops the script by marking it as not running.
 * @returns {void}
 */
function stopRunning() {
  fs.writeFileSync(IS_RUNNING_FILE, 'false');
}

/**
 * Gets the current timestamp in ISO format.
 * @returns {string} The current timestamp.
 */
function getTimestamp() {
  const now = new Date();
  return now.toISOString();
}

/**
 * Cleans the error message by removing ANSI color codes.
 * @param {string} errorMessage - The error message to clean.
 * @returns {string} The cleaned error message.
 */
function cleanErrorMessage(errorMessage) {
  return errorMessage
    .replace(/\[90m/g, '')
    .replace(/\[36m/g, '')
    .replace(/\[37m/g, '')
    .replace(/\[46m/g, '')
    .replace(/\[30m/g, '')
    .replace(/\[0m/g, '');
}

/**
 * Sends debug information to a Discord webhook.
 * @param {string} content - The content to send in the Discord message.
 * @returns {Promise<void>}
 */
async function sendDebugInfoToDiscord(content) {
  try {
    const webhookURL = process.env.DEV_DISCORD_WEBHOOK;
    if (!webhookURL) {
      console.error('Discord webhook URL is not defined in the environment variables.');
      return;
    }
    const payload = {
      content: content,
    };
    const response = await axios.post(webhookURL, payload);
    if (response.status !== 204) {
      console.error('Failed to send Discord webhook. Status code:', response.status);
    }
  } catch (error) {
    console.error(`${COLORS.RED}Error sending Discord webhook:${COLORS.RESET}\n${error.message}`);
  }
}

/**
 * Sleep for the specified number of milliseconds.
 * @param {number} ms - Milliseconds to sleep.
 * @returns {Promise<void>} - Promise that resolves after the specified delay.
 */
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Start the replacing process.
 * @returns {void}
 */
function startReplacingProcess() {
  try {
    console.info(`${COLORS.BG_CYAN}${COLORS.BLACK}[INFO] Starting replacing process...${COLORS.RESET}`);
    cp.execSync('node app/3replace/1separateActors.js', { stdio: 'inherit' });
    console.info(`${COLORS.BG_CYAN}${COLORS.BLACK}[INFO] Replacing process completed successfully.${COLORS.RESET}`);
  } catch (error) {
    console.error(`${COLORS.RED}[ERROR] Error in replacing process: ${error.message}${COLORS.RESET}`);
    process.exit(1);
  }
}

/**
 * Sanitize the Anthropic API output by removing unwanted tags and fixing common issues.
 * @param {string} output - Output from the Anthropic API.
 * @returns {string} - Sanitized output.
 */
function sanitizeAnthropicOutput(output) {
  return (
    output
      // @ts-ignore
      .replaceAll('<Translation>', '')
      .replaceAll('</Translation>', '')
      .replaceAll('-samy', '-sama')
      .replaceAll('-samą', '-sama')
      .replaceAll('-samę', '-sama')
      .replaceAll('-samej', '-sama')
      .replaceAll('-samie', '-sama')
      .replaceAll('senpaia', 'senpai')
      .replaceAll('senpaiem', 'senpai')
      .replaceAll('senpaiu', 'senpai')
      .replaceAll('sempai', 'senpai')
      .replaceAll('-kunowi', '-kun')
      .replaceAll('-kunie', '-kun')
      .replaceAll('-kunem', '-kun')
      .replaceAll('-kuna', '-kun')
      .replaceAll('-chanie', '-chan')
      .replaceAll('-chana', '-chan')
      .replaceAll('-chanowi', '-chan')
      .replaceAll('-sanowi', '-san')
      .replaceAll('-sanem', '-san')
      .replaceAll('-sanie', '-san')
      .replaceAll('-sana', '-san')
      .replaceAll('."', '".')
      .replaceAll('  ', ' ')
  );
}

function createExamplesMessage() {
  return `
  <examples>
  <example>
    <English_Source>S-Silky hair! Small face! Big eyes! So cute! Even her voice is cute!</English_Source>
    <ideal_output>J-Jedwabiste włosy! Mała twarz! Duże oczy! Taka urocza! Nawet jej głos jest
      uroczy!</ideal_output>
  </example>

  <example>
    <English_Source>I will be your live-in assistant starting today.</English_Source>
    <ideal_output>Od dzisiaj będę twoją asystentką mieszkająca na miejscu</ideal_output>
  </example>

  <example>
    <English_Source>What did you mean by "live-in"?</English_Source>
    <ideal_output>Co masz na myśli przez "mieszkającą na miejscu"?</ideal_output>
  </example>

  <example>
    <English_Source>By using the Red Star to become a fake Seven Star,</English_Source>
    <ideal_output>Używając Czerwonej Gwiazdy by stać się fałszywym Siedmiogwiazdkowym,</ideal_output>
  </example>

  <example>
    <English_Source>I'm a second-year student at Ouka Academy, Kazami Suzuran. Meow!</English_Source>
    <ideal_output>Uczennica drugiego roku Akademii Ouka, Kazami Suzuran. Miau!</ideal_output>
  </example>

  <example>
    <English_Source>Urasaka-senpai's main ability is Remove Speed Limit.</English_Source>
    <ideal_output>Główną umiejętnością Urasaki-senpai jest Usuń Limit Prędkości.</ideal_output>
  </example>

  <example>
    <English_Source>If the Headmistress requested it, we're in the same boat.</English_Source>
    <ideal_output>Jeśli tak zarządziła Dyrektorka, to jedziemy na tym samym wózku</ideal_output>
  </example>

  <example>
    <English_Source>On the third basement floor of the dungeon,</English_Source>
    <ideal_output>Na trzecim poziomie podziemni lochu,</ideal_output>
  </example>

  <example>
    <English_Source>through the underground graveyard and past the forest of the spire,</English_Source>
    <ideal_output>mijając podziemny cmentarz i las przy iglicy,</ideal_output>
  </example>

  <example>
    <English_Source>The once splendid castle,</English_Source>
    <ideal_output>Ten niegdyś wspaniały zamek,</ideal_output>
  </example>

  <example>
    <English_Source>are you using Golems as a garden?</English_Source>
    <ideal_output>używasz golemów jako ogrodu?</ideal_output>
  </example>

  <example>
    <English_Source>The people in this castle will weep!</English_Source>
    <ideal_output>Ludzie w zamku się załamią!</ideal_output>
  </example>

  <example>
    <English_Source>And so will scholars of magic!</English_Source>
    <ideal_output>Uczeni magowie również!</ideal_output>
  </example>

  <example>
    <English_Source>It's illegal to activate a magical creature without permission!</English_Source>
    <ideal_output>Bez zezwolenia nie wolno aktywować magicznych stworzeń!</ideal_output>
  </example>

  <example>
    <English_Source>Stir the soil well and make raised rows.</English_Source>
    <ideal_output>Mieszaj dobrze ziemię i układaj grządki.</ideal_output>
  </example>

  <example>
    <English_Source>Drinking water made me want to use the bathroom.</English_Source>
    <ideal_output>Przez tę wodę muszę iść do toalety.</ideal_output>
  </example>

  <example>
    <English_Source>Well then, while that elven girl is gone,</English_Source>
    <ideal_output>Dobra, teraz gdy ta elfka sobie poszła,</ideal_output>
  </example>

  <example>
    <English_Source>I'll gather up the scattered soil as much as I can.</English_Source>
    <ideal_output>Spróbuję zebrać tyle ziemi, ile tylko mogę.</ideal_output>
  </example>

  <example>
    <English_Source>Will burying those make them revive quickly?</English_Source>
    <ideal_output>Zakopanie ich sprawi, że szybciej się zregenerują?</ideal_output>
  </example>

  <example>
    <English_Source>Is this size really okay?</English_Source>
    <ideal_output>Jesteś pewny, że to dobry rozmiar?</ideal_output>
  </example>

  <example>
    <English_Source>When you make something easier, you may lose your edge in a skill.</English_Source>
    <ideal_output>Gdy coś sobie ułatwiasz, możesz stracić wprawę w danej umiejętności.</ideal_output>
  </example>

  <example>
    <English_Source>I'll strike a flint, so it's fine.</English_Source>
    <ideal_output>Poradzę sobie, użyję krzemienia.</ideal_output>
  </example>

  <example>
    <English_Source>Man, thought you'd be off the clock by five? They still called you in? Shitluck.</English_Source>
    <ideal_output>Stary, myślałem, że o 17 będziesz po robocie? Jednak cię wezwali? Do dupy.</ideal_output>
  </example>

  <example>
    <English_Source>Cap said I could leave my gun and badge if I'd rather go home</English_Source>
    <ideal_output>Szef mówił, że jak wolę iść do domu to mogę się pożegnać z bronią i odznaką,</ideal_output>
  </example>

  <example>
    <English_Source>and listen to my old lady go on and on about online shopping or whatever.</English_Source>
    <ideal_output>i słuchać gadania mojej staruszki o zakupach online czy czymś tam.</ideal_output>
  </example>

  <example>
    <English_Source>Hmm? What the hell you doin'? Back it up.</English_Source>
    <ideal_output>Hm? Co ty odpierdalasz? Cofnij się.</ideal_output>
  </example>

  <example>
    <English_Source>Looking to get shot?</English_Source>
    <ideal_output>Chcesz kulkę?</ideal_output>
  </example>

  <example>
    <English_Source>Got us a cyber psycho!</English_Source>
    <ideal_output>Mamy tu cyberpsychola!</ideal_output>
  </example>

  <example>
    <English_Source>Freak's got military-grade ICE!</English_Source>
    <ideal_output>Świr ma wojskowy ICE!</ideal_output>
  </example>

  <example>
    <English_Source>Meaning that our tech isn't? Get to it!</English_Source>
    <ideal_output>A co, my niby nie? Brać go!</ideal_output>
  </example>

  <example>
    <English_Source>That's what you get for choosing the life of a public servant.</English_Source>
    <ideal_output>Tak to już jest, gdy wybierzesz życie funkcjonariusza publicznego.</ideal_output>
  </example>

  <example>
    <English_Source>It just had to fucking rain...</English_Source>
    <ideal_output>Jeszcze musiało kurwa padać...</ideal_output>
  </example>

  <example>
    <English_Source>I use it when things get unbearable.</English_Source>
    <ideal_output>Używam go, gdy coś mnie przerasta.</ideal_output>
  </example>

  <example>
    <English_Source>Spirits of memory, deliver unto oblivion.</English_Source>
    <ideal_output>Duchy pamięci, dajcie mu zapomnienie.</ideal_output>
  </example>

  <example>
    <English_Source>HISTORY OF ROOM SHARE</English_Source>
    <ideal_output>HISTORIA WSPÓŁLOKATORSTWA</ideal_output>
  </example>

  <example>
    <English_Source>is old enough to recruit friends and live with them.</English_Source>
    <ideal_output>jest dość dorosły, by zamieszkać ze znajomymi.</ideal_output>
  </example>

  <example>
    <English_Source>I'M DONE BEING YOUR FAN ARE YOU TODAY YEARS OLD?</English_Source>
    <ideal_output>MAM DOŚC BYCIA TWOIM FANEM. CO? DZISIAJ SIĘ URODZIŁEŚ?</ideal_output>
  </example>

  <example>
    <English_Source>AGAIN POPS IS RED IN THE FACE HE MAKES UP HIS OWN RULES</English_Source>
    <ideal_output>DZIADEK ZNOWU SIĘ WŚCIEKA I WYMYŚLA WŁASNE ZASADY</ideal_output>
  </example>

  <example>
    <English_Source>Shinpei, make sure to find me.</English_Source>
    <ideal_output>Obiecaj, że mnie znajdziesz, Shinpei.</ideal_output>
  </example>

  <example>
    <English_Source>That was the first time someone's ever slapped me.</English_Source>
    <ideal_output>Pierwszy raz dostałem od kogoś liścia.</ideal_output>
  </example>

  <example>
    <English_Source>With a population of around 700,</English_Source>
    <ideal_output>Mieszka tam około 700 mieszkańców,</ideal_output>
  </example>

  <example>
    <English_Source>Sis would get mad if she saw me crying like this.</English_Source>
    <ideal_output>Siorka byłaby zła, widząc, jak płaczę.</ideal_output>
  </example>

  <example>
    <English_Source>I don't recall neglecting mana detection.</English_Source>
    <ideal_output>Przecież nie zaniedbałem wykrywania many.</ideal_output>
  </example>

  <example>
    <English_Source>Even with its back turned, I still don't detect any openings.</English_Source>
    <ideal_output>Nawet gdy jest odwrócone plecami, nie dostrzegam żadnych możliwości ataku.</ideal_output>
  </example>

  <example>
    <English_Source>So what? That spell is easily blocked by defensive magic.</English_Source>
    <ideal_output>I co z tego? Magia obronna z łatwością zablokuje to zaklęcie.</ideal_output>
  </example>

  <example>
    <English_Source>Take this!</English_Source>
    <ideal_output>A masz!</ideal_output>
  </example>

  <example>
    <English_Source>My replica is back in action!</English_Source>
    <ideal_output>Moja replika powróciła.</ideal_output>
  </example>

  <example>
    <English_Source>Will this keep going until that Spiegel thing is defeated?</English_Source>
    <ideal_output>Będzie się to tak ciągnąć dopóki nie pokonamy tego całego Spiegela?</ideal_output>
  </example>

  <example>
    <English_Source>The vulnerability in my mana detection isn't enough to kill me.</English_Source>
    <ideal_output>Wykorzystanie mojego słabego punktu w wykrywaniu many nie jest wystarczające żeby
      mnie zabić</ideal_output>
  </example>

  <example>
    <English_Source>We could win with a bigger opening, though.</English_Source>
    <ideal_output>Ale gdybym stworzyła lepszą szansę na atak, moglibyśmy wygrać.</ideal_output>
  </example>

  <example>
    <English_Source>If I show an opening, then the replica wll show an even greater one.</English_Source>
    <ideal_output>Jeśli się odsłonię, replika odsłoni się jeszcze bardziej</ideal_output>
  </example>

  <example>
    <English_Source>If I focus on defense, it won't be fatal</English_Source>
    <ideal_output>Jeśli skupię się na obronie, nie umrę.</ideal_output>
  </example>

  <example>
    <English_Source>You've left yourself wide open.</English_Source>
    <ideal_output>Całkowicie się odsłoniłaś.</ideal_output>
  </example>

  <example>
    <English_Source>So, where are the ladies of the hour?</English_Source>
    <ideal_output>A gdzie są nasze bohaterki?</ideal_output>
  </example>

  <example>
    <English_Source>We can't beat them in a disorganized free-for-all</English_Source>
    <ideal_output>W chaotycznej walce nie mamy z nimi szans.</ideal_output>
  </example>

  <example>
    <English_Source>Use numbers to deal with anyone we can't fight alone.</English_Source>
    <ideal_output>Tych, z którymi nie damy sobie rady w pojedynkę, zaatakujmy w grupie.</ideal_output>
  </example>

  <example>
    <English_Source>You say that every time.</English_Source>
    <ideal_output>Słyszę to za każdym razem.</ideal_output>
  </example>

  <example>
    <English_Source>She's pissed? Why?!</English_Source>
    <ideal_output>Czemu jesteś wnerwiona?!</ideal_output>
  </example>

  <example>
    <English_Source>Ms. Frieren and I had an argument.</English_Source>
    <ideal_output>Pokłociłam się z panią Frieren.</ideal_output>
  </example>

  <example>
    <English_Source>You mean that staff, don't you?</English_Source>
    <ideal_output>Mówisz o swojej różdzce?</ideal_output>
  </example>

  <example>
    <English_Source>She said it couldn't be repaired since it was already in pieces.</English_Source>
    <ideal_output>Wspomniała, że jej uszkodzenia są zbyt rozległe by ją naprawić.</ideal_output>
  </example>

  <example>
    <English_Source>Still, Mr. Heiter gave me that staff.</English_Source>
    <ideal_output>Mimo wszystko podarował mi ją Pan Heiter.</ideal_output>
  </example>

  <example>
    <English_Source>It's been with me ever since I was little.</English_Source>
    <ideal_output>Miałam ją, odkąd tylko pamiętam.</ideal_output>
  </example>

  <example>
    <English_Source>We're talking about Frieren, though. I doubt she meant any harm.</English_Source>
    <ideal_output>Wątpię, że Frieren chciałaby ci jakoś zaszkodzić.</ideal_output>
  </example>

  <example>
    <English_Source>Just make fun of me like you always do.</English_Source>
    <ideal_output>Ponabijaj się ze mnie, jak to zawsze robisz.</ideal_output>
  </example>

  <example>
    <English_Source>Do you want me to?</English_Source>
    <ideal_output>A chcesz?</ideal_output>
  </example>

  <example>
    <English_Source>In that case, Little Miss Lawine, shall I pat your head?</English_Source>
    <ideal_output>To może cię pogłaskać po główce, Lawine?</ideal_output>
  </example>

  <example>
    <English_Source>What? You wanna fight?</English_Source>
    <ideal_output>Co? Szukasz guza?</ideal_output>
  </example>

  <example>
    <English_Source>You're in really rough shape.</English_Source>
    <ideal_output>Krucho z tobą.</ideal_output>
  </example>

  <example>
    <English_Source>Hey, how long are you going to hang around here?</English_Source>
    <ideal_output>Ej, długo będziesz się tak jeszcze kręcić?</ideal_output>
  </example>

  <example>
    <English_Source>I failed the test. There's nothing keeping us together anymore.</English_Source>
    <ideal_output>Oblałem egzamin, nic nas już nie łączy.</ideal_output>
  </example>

  <example>
    <English_Source>You're interfering with my business. Get out of here.</English_Source>
    <ideal_output>Przeszkadzasz mi w robocie, idź se stąd.</ideal_output>
  </example>

  <example>
    <English_Source>I'm here as a customer.</English_Source>
    <ideal_output>Przyszedłem tu jako klient.</ideal_output>
  </example>

  <example>
    <English_Source>Then hurry up and choose something.</English_Source>
    <ideal_output>No to wybierz coś w końcu.</ideal_output>
  </example>

  <example>
    <English_Source>It could've happened to anyone in that position.</English_Source>
    <ideal_output>Każdego mógłby spotkać taki los.</ideal_output>
  </example>

  <example>
    <English_Source>A loss is a loss. I wasn't strong enough.</English_Source>
    <ideal_output>Porażka, to porażka, po prostu zabrakło mi umiejętności.</ideal_output>
  </example>

  <example>
    <English_Source>A decrepit old man like you might not be around three years from now.</English_Source>
    <ideal_output>Taki zniedołężniały staruszek może już nie zdążyć na kolejny.</ideal_output>
  </example>

  <example>
    <English_Source>Richter, you truly are an insolent youngster.</English_Source>
    <ideal_output>Richter, jesteś bezczelnym młodzieńcem.</ideal_output>
  </example>

  <example>
    <English_Source>You mock authority</English_Source>
    <ideal_output>Kpisz z autorytetów</ideal_output>
  </example>

  <example>
    <English_Source>and have no qualms about mistreating the weak in order to achieve your goals.</English_Source>
    <ideal_output>i posuniesz się do wszystkiego by osiągnąć swój cel.</ideal_output>
  </example>

  <example>
    <English_Source>Your behavior is hardly praiseworthy.</English_Source>
    <ideal_output>Nie zasługujesz na żadne pochwały.</ideal_output>
  </example>

  <example>
    <English_Source>And yet I feel no hatred for you at all,</English_Source>
    <ideal_output>Mimo to nie czuję do ciebie żadnej urazy.</ideal_output>
  </example>

  <example>
    <English_Source>probably because I was once an insolent youngster too.</English_Source>
    <ideal_output>może przypominasz mi dawnego mnie.</ideal_output>
  </example>

  <example>
    <English_Source>And now, I'm an imperial mage.</English_Source>
    <ideal_output>Jednak stałem się nadwornym magiem.</ideal_output>
  </example>

  <example>
    <English_Source>What are you trying to say?</English_Source>
    <ideal_output>Do czego zmierzasz?</ideal_output>
  </example>

  <example>
    <English_Source>I'm telling you not to be so discouraged.</English_Source>
    <ideal_output>Nie zniechęcaj się.</ideal_output>
  </example>

  <example>
    <English_Source>Three years from now, you'll be far stronger than you are today.</English_Source>
    <ideal_output>Za trzy lata będziesz o wiele potężniejszy.</ideal_output>
  </example>

  <example>
    <English_Source>Laufen, let's go.</English_Source>
    <ideal_output>Laufen, chodźmy.</ideal_output>
  </example>

  <example>
    <English_Source>Sorry, the old man's not good with people.</English_Source>
    <ideal_output>Wybacz, dziadek nie jest najlepszy w takie rozmowy.</ideal_output>
  </example>

  <example>
    <English_Source>What's your relationship with Denken, exactly?</English_Source>
    <ideal_output>Kim ty właściwie jesteś dla Denkena?</ideal_output>
  </example>

  <example>
    <English_Source>Damn it, old man.</English_Source>
    <ideal_output>Cholerny staruszek.</ideal_output>
  </example>

  <example>
    <English_Source>He didn't buy anything after all.</English_Source>
    <ideal_output>Koniec końców, niczego nie kupił.</ideal_output>
  </example>

  <example>
    <English_Source>Is today my unlucky day or something?</English_Source>
    <ideal_output>Rany, naprawdę mam dzisiaj pecha.</ideal_output>
  </example>

  <example>
    <English_Source>I asked around town and was told this shop can fix any staff, no matter how
      broken.</English_Source>
    <ideal_output>Pytałam trochę na mieście i podobno umiesz naprawić każdą różdzkę, nawet jakby
      była w drzazgach.</ideal_output>
  </example>

  <example>
    <English_Source>What is this broken pile of scrapwood? Don't tell me this is meant to be a
      staff.</English_Source>
    <ideal_output>Co to za sterta drzazg? I to była kiedyś różdzka?</ideal_output>
  </example>

  <example>
    <English_Source>I'm not a trash disposal service, you know.</English_Source>
    <ideal_output>Przecież to są jakieś śmieci.</ideal_output>
  </example>

  <example>
    <English_Source>My mood was starting to improve, but because of you, I feel terrible again.</English_Source>
    <ideal_output>Zaczynałem odzyskiwać humor ale dzięki tobie znów czuje się beznadziejnie.</ideal_output>
  </example>

  <example>
    <English_Source>I have the right to choose which jobs I work on.</English_Source>
    <ideal_output>Dobrze, że mogę wybierać sobie zlecenia.</ideal_output>
  </example>

  <example>
    <English_Source>You should give up on this staff.</English_Source>
    <ideal_output>Powinnaś kupić nową różdzkę.</ideal_output>
  </example>

  <example>
    <English_Source>I see.</English_Source>
    <ideal_output>No dobra.</ideal_output>
  </example>

  <example>
    <English_Source>If you can't do it, then never mind.</English_Source>
    <ideal_output>Skoro nie dasz rady to trudno.</ideal_output>
  </example>

  <example>
    <English_Source>Frieren, you really get on my nerves.</English_Source>
    <ideal_output>Ale działasz mi na nerwy, Frieren.</ideal_output>
  </example>

  <example>
    <English_Source>You're Frieren's kid.</English_Source>
    <ideal_output>Jesteś dzieciakiem Frieren.</ideal_output>
  </example>

  <example>
    <English_Source>Her student, right?</English_Source>
    <ideal_output>Uczennicą, co nie?</ideal_output>
  </example>

  <example>
    <English_Source>Either way, the second test only ended yesterday,</English_Source>
    <ideal_output>W każdym razie, drugi test ledwo co się wczoraj skończył</ideal_output>
  </example>

  <example>
    <English_Source>but I had another terrible day because of your master.</English_Source>
    <ideal_output>a twoja mistrzyni zrujnowała mi kolejny dzień</ideal_output>
  </example>

  <example>
    <English_Source>I would've made more profit if you'd just bought a new one.</English_Source>
    <ideal_output>Zarobiłbym o wiele więcej, gdyby kupila jakąś nową</ideal_output>
  </example>

  <example>
    <English_Source>Frieren struggles to understand emotions.</English_Source>
    <ideal_output>Frieren ma problemy ze zrozumieniem ludzkich uczuć.</ideal_output>
  </example>

  <example>
    <English_Source>I'm sure it will cause difficulties and disagreements.</English_Source>
    <ideal_output>Najpewniej spowoduje to wiele nieporozumień i trudności.</ideal_output>
  </example>

  <example>
    <English_Source>But there is one good thing about it.</English_Source>
    <ideal_output>Jednak ma to też dobre strony.</ideal_output>
  </example>

  <example>
    <English_Source>Frieren will worry and care about you just as much.</English_Source>
    <ideal_output>Będzie się o ciebie troszczyć i dbać z całego serca.</ideal_output>
  </example>

  <example>
    <English_Source>Few masters are better than her.</English_Source>
    <ideal_output>Trudno znaleźć lepszą mistrzynię.</ideal_output>
  </example>

  <example>
    <English_Source>Thank you as always, Richter.</English_Source>
    <ideal_output>Zawsze można na tobie polegać, Richter.</ideal_output>
  </example>

  <example>
    <English_Source>Sure thing.</English_Source>
    <ideal_output>Drobiazg.</ideal_output>
  </example>

  <example>
    <English_Source>She's such a pain in the butt.</English_Source>
    <ideal_output>Z nią zawsze są problemy...</ideal_output>
  </example>

  <example>
    <English_Source>Then allow me to escort you today, Little Miss Lawine.</English_Source>
    <ideal_output>Pozwól więc, że odprowadzę cię dzisiaj do domu, panienko Lawine.</ideal_output>
  </example>

  <example>
    <English_Source>I'm looking for a front-liner.</English_Source>
    <ideal_output>Potrzebuję wojownika.</ideal_output>
  </example>

  <example>
    <English_Source>It'll be a pain to keep having to catch her if we fly back.</English_Source>
    <ideal_output>Upierdliwe będzie ciągłe łapanie jej w drodze powrotnej.</ideal_output>
  </example>

  <example>
    <English_Source>I have to work harder too.</English_Source>
    <ideal_output>Muszę się bardziej postarać.</ideal_output>
  </example>

  <example>
    <English_Source>I don't think I have it in me to kill someone.</English_Source>
    <ideal_output>Chyba nie mam w sobie tego czegoś, by kogoś zabić.</ideal_output>
  </example>

  <example>
    <English_Source>I figured that was propably the case.</English_Source>
    <ideal_output>Trochę, się tego domyślałem.</ideal_output>
  </example>

  <example>
    <English_Source>Where'd this come from? It's so dark, I can't even tell who won.</English_Source>
    <ideal_output>Czemu tak nagle? Jest tak ciemno, że nie widzę kto wygrał.</ideal_output>
  </example>

  <example>
    <English_Source>Bit the customer gets to be selfish. That's what they're paying me for.</English_Source>
    <ideal_output>Klient ma prawo być wymagający, dlatego tyle mi płacą.</ideal_output>
  </example>

  <example>
    <English_Source>Flunking Reimi!</English_Source>
    <ideal_output>Oblewająca Reimi!</ideal_output>
  </example>

  <example>
    <English_Source>Akira's not off at all!</English_Source>
    <ideal_output>Z Akirą wszystko w porządku!</ideal_output>
  </example>

  <example>
    <English_Source>After I finally dried off?!</English_Source>
    <ideal_output>Dopiero co wyschłem!</ideal_output>
  </example>

  <example>
    <English_Source>You did it right in front of me that time!</English_Source>
    <ideal_output>Tym razem widziałam jak to zrobiłeś!</ideal_output>
  </example>

  <example>
    <English_Source>Though it could also be that they chose an idol voice actress</English_Source>
    <ideal_output>Chociaż może być też tak, że wybrali seiyuu będącą idolką</ideal_output>
  </example>

  <example>
    <English_Source>That's the spirit.</English_Source>
    <ideal_output>O to chodzi.</ideal_output>
  </example>

  <example>
    <English_Source>Shithead...</English_Source>
    <ideal_output>Sukinsyn...</ideal_output>
  </example>

  <example>
    <English_Source>You're wide open!</English_Source>
    <ideal_output>Opuściłaś gardę!</ideal_output>
  </example>

  <example>
    <English_Source>I'm going to punch each one of you in turn!</English_Source>
    <ideal_output>Przywalę każdemu z was po kolei!</ideal_output>
  </example>

  <example>
    <English_Source>How could you kick a sweet dog like this?!</English_Source>
    <ideal_output>Jak mogłaś kopnąć takiego słodziaka?!</ideal_output>
  </example>

  <example>
    <English_Source>Here goes nothing.</English_Source>
    <ideal_output>Raz kozie śmierć.</ideal_output>
  </example>

  <example>
    <English_Source>You got nothing, all right.</English_Source>
    <ideal_output>No, śmierć to ty tu znajdziesz.</ideal_output>
  </example>

  <example>
    <English_Source>Security personnel, deploy at once.</English_Source>
    <ideal_output>Personel ochrony proszony jest o interwencję.</ideal_output>
  </example>

  <example>
    <English_Source>I prioritized you over retrieving my friend's id.</English_Source>
    <ideal_output>Wolałam ocalić ciebie niż odzyskać id przyjaciółki.</ideal_output>
  </example>

  <example>
    <English_Source>She didn't even have a running start! Not even a pro could do that!</English_Source>
    <ideal_output>Nawet się nie rozpędziła! Nawet prosi tak nie potrafią!</ideal_output>
  </example>

  <example>
    <English_Source>Just a spell-stack...</English_Source>
    <ideal_output>Prosta kumulacja zaklęć...</ideal_output>
  </example>

  <example>
    <English_Source>A single stack is gonna have two or maybe three spells at once!</English_Source>
    <ideal_output>W jednej kumulacji mogą być co najwyżej dwa, może trzy zaklęcia naraz!</ideal_output>
  </example>

  <example>
    <English_Source>Say your prayers!</English_Source>
    <ideal_output>Zmów paciorek!</ideal_output>
  </example>

  <example>
    <English_Source>Consider it done!</English_Source>
    <ideal_output>Masz to jak w banku!</ideal_output>
  </example>

  <example>
    <English_Source>You could say I'm brethless!</English_Source>
    <ideal_output>Aż mi zaparło dech w piersi!</ideal_output>
  </example>

  <example>
    <English_Source>On the other hand, I get the feeling that you'll be able to qi-p me around
      pretty easy!</English_Source>
    <ideal_output>Z drugiej strony, mam wrażenie, że pozwoli to na dłu-qi związek!</ideal_output>
  </example>

  <example>
    <English_Source>Do you really think I'd accept these ridiculous answers?</English_Source>
    <ideal_output>Naprawdę myślisz, że uznam ci te bzdury?</ideal_output>
  </example>

  <example>
    <English_Source>Agano got off easy with animals.</English_Source>
    <ideal_output>Agano upiekło się ze zwierzętami.</ideal_output>
  </example>

  <example>
    <English_Source>Shizuru-chan's not here, either.</English_Source>
    <ideal_output>Shizuru-chan też coś nie ma.</ideal_output>
  </example>

  <example>
    <English_Source>Oh, career stuff, huh?</English_Source>
    <ideal_output>Doradztwo zawodowe?</ideal_output>
  </example>

  <example>
    <English_Source>Not that there's any point to discussing it.</English_Source>
    <ideal_output>A na co to komu?</ideal_output>
  </example>

  <example>
    <English_Source>Half of them got cold feet and came running back.</English_Source>
    <ideal_output>Połowa z nich szybko wróciła, przerażona tym co tam widzieli.</ideal_output>
  </example>

  <example>
    <English_Source>If you let me down, I'm not going to be happy.</English_Source>
    <ideal_output>Lepiej żebyś mnie nie zawiódł.</ideal_output>
  </example>

  <example>
    <English_Source>I was forced to dress up like that though...</English_Source>
    <ideal_output>Tyle, że ja zostałam zmuszona by się tak ubrać...</ideal_output>
  </example>

  <example>
    <English_Source>That's basically correct but also kind of aggravating.</English_Source>
    <ideal_output>W zasadzie masz rację, chociaż wolałabym, żebyś jej nie miała...</ideal_output>
  </example>

  <example>
    <English_Source>I shall not yield to violence!</English_Source>
    <ideal_output>Przemocą mnie nie złamiesz!</ideal_output>
  </example>

  <example>
    <English_Source>It still doesn't feel real.</English_Source>
    <ideal_output>Nadal wydaję się to snem.</ideal_output>
  </example>

  <example>
    <English_Source>but I guess I really did get dragged into something you'd normally associate
      with fantasy.</English_Source>
    <ideal_output>ale chyba naprawdę wciągnięto mnie w coś co można znaleźć tylko w fantastyce.</ideal_output>
  </example>

  <example>
    <English_Source>I suppose I am a little excited, though.</English_Source>
    <ideal_output>Nawet trochę jestem podekscytowana.</ideal_output>
  </example>

  <example>
    <English_Source>I figured I might as well enjoy it.</English_Source>
    <ideal_output>Skoro już do tego doszło, to nie ma co stawiać oporu.</ideal_output>
  </example>

  <example>
    <English_Source>Only because you pretty much forced me.</English_Source>
    <ideal_output>Tylko dlatego, że mnie praktycznie zmusiłaś.</ideal_output>
  </example>

  <example>
    <English_Source>Why did you bother hiding?</English_Source>
    <ideal_output>Czemu musiałaś się schować?</ideal_output>
  </example>

  <example>
    <English_Source>Because it's embarassing.</English_Source>
    <ideal_output>Bo się trochę wstydzę.</ideal_output>
  </example>

  <example>
    <English_Source>Adding imaginary numerical axis to the fifth instrumental variable.</English_Source>
    <ideal_output>Dodawanie urojonej osi liczbowej do piątej zmiennej instrumentalnej.</ideal_output>
  </example>

  <example>
    <English_Source>Performing partial inversion of Mirror World connections.</English_Source>
    <ideal_output>Wykonywanie częściowej inwersji połączeń ze Światem Lustrzanym.</ideal_output>
  </example>

  <example>
    <English_Source>Class Card Lancer - Include.</English_Source>
    <ideal_output>Karta Klasy Lancer - Przywołanie.</ideal_output>
  </example>

  <example>
    <English_Source>Lancer - Uninclude.</English_Source>
    <ideal_output>Lancer - Odwołanie.</ideal_output>
  </example>

  <example>
    <English_Source>and do your best to finish this in a way that deals with Tohsaka Rin as well!</English_Source>
    <ideal_output>i postaraj się ją zakończyć w sposób jaki zakończy również Tohsakę Rin!</ideal_output>
  </example>

  <example>
    <English_Source>...among other things.</English_Source>
    <ideal_output>...i nie tylko.</ideal_output>
  </example>

  <example>
    <English_Source>Mid-tier barrage!</English_Source>
    <ideal_output>Ostrzał średniego poziomu!</ideal_output>
  </example>

  <example>
    <English_Source>The timing's good! We can do this!</English_Source>
    <ideal_output>Dobre wyczucie czasu! Możemy to wygrać!</ideal_output>
  </example>

  <example>
    <English_Source>The enchantment can't be that good...</English_Source>
    <ideal_output>Te wzmocnienie i tak pewnie nie jest dobre...</ideal_output>
  </example>

  <example>
    <English_Source>That's plenty rare, too.</English_Source>
    <ideal_output>To też jest dość rzadkie.</ideal_output>
  </example>

  <example>
    <English_Source>No, he might be even too good for the rank of swordmaster</English_Source>
    <ideal_output>Nie, może być zbyt zdolny nawet na rangę arcymistrza miecza!</ideal_output>
  </example>

  <example>
    <English_Source>I sensed a hottie... er, a creature's presence, and came running!</English_Source>
    <ideal_output>Wyczułam przystojniaka... znaczy obecność potwora i przybiegłam!</ideal_output>
  </example>

  <example>
    <English_Source>The only thing you're fit to feel... is my master's blade.</English_Source>
    <ideal_output>Jedyną rzecz którą jesteś godny poczuć... to ostrze mego pana.</ideal_output>
  </example>

  <example>
    <English_Source>They're all head over heels for your mana, Prince Lloyd!</English_Source>
    <ideal_output>Zakochały się w twojej manie, Książe Lloyd!</ideal_output>
  </example>

  <example>
    <English_Source>And with you, I can cut loose without any restraint!</English_Source>
    <ideal_output>A przy tobie nie muszę się w ogóle powstrzymywać!</ideal_output>
  </example>

  <example>
    <English_Source>I shouldn't count my chickens before they hatch</English_Source>
    <ideal_output>Nie powinienem chwalić dnia przed zachodem słońca</ideal_output>
  </example>

  <example>
    <English_Source>A bird in hand is worth two in the bush.</English_Source>
    <ideal_output>Lepszy wróbel w garści niż gołąb na dachu.</ideal_output>
  </example>

  <example>
    <English_Source>A drowning man will clutch at a straw.</English_Source>
    <ideal_output>Tonący brzytwy się chwyta.</ideal_output>
  </example>

  <example>
    <English_Source>A friend in need is a friend indeed.</English_Source>
    <ideal_output>Prawdziwych przyjaciół poznaje się w biedzie.</ideal_output>
  </example>

  <example>
    <English_Source>A leopard doesn't change its spots.</English_Source>
    <ideal_output>Ludzie nigdy się nie zmieniają.</ideal_output>
  </example>

  <example>
    <English_Source>All good things come to an end.</English_Source>
    <ideal_output>Wszystko, co dobre, szybko się kończy.</ideal_output>
  </example>

  <example>
    <English_Source>All's well that ends well.</English_Source>
    <ideal_output>Wszystko dobre, co się dobrze kończy.</ideal_output>
  </example>

  <example>
    <English_Source>All that glitters is not gold.</English_Source>
    <ideal_output>Nie wszystko złoto, co się świeci.</ideal_output>
  </example>

  <example>
    <English_Source>Beauty is in the eye of the beholder</English_Source>
    <ideal_output>Nie to ładne, co ładne, ale co się komu podoba.</ideal_output>
  </example>

  <example>
    <English_Source>Better safe than sorry.</English_Source>
    <ideal_output>Lepiej dmuchać na zimne.</ideal_output>
  </example>

  <example>
    <English_Source>Haste makes waste.</English_Source>
    <ideal_output>Co nagle, to po diable.</ideal_output>
  </example>

  <example>
    <English_Source>Curiosity killed the cat.</English_Source>
    <ideal_output>Ciekawość to pierwszy stopień do piekła.</ideal_output>
  </example>

  <example>
    <English_Source>And while it's been a mixed bag,</English_Source>
    <ideal_output>I choć mam myśli przeróżne.</ideal_output>
  </example>

  <example>
    <English_Source>It's a mixed bag</English_Source>
    <ideal_output>Różnie z tym jest.</ideal_output>
  </example>

  <example>
    <English_Source>And Aqua, just give up already!</English_Source>
    <ideal_output>A ty Aqua daj już spokój!</ideal_output>
  </example>

  <example>
    <English_Source>If we have a horse run to the Trenni castle,</English_Source>
    <ideal_output>Jeśli udamy się konno do zamku w Trenni,</ideal_output>
  </example>

  <example>
    <English_Source>I have not spoken properly with anyone for hundreds of years.</English_Source>
    <ideal_output>Nie rozmawiałam porządnie z nikim od setek lat.</ideal_output>
  </example>

  <example>
    <English_Source>I can handle a few more rounds.</English_Source>
    <ideal_output>Dam sobie radę jeszcze z kilkoma.</ideal_output>
  </example>

  <example>
    <English_Source>Yes. I don't think I can get enough of this feeling.</English_Source>
    <ideal_output>Ta. Czuję, że nigdy nie będę miała dość tego uczucia.</ideal_output>
  </example>

</examples>
  `;
}